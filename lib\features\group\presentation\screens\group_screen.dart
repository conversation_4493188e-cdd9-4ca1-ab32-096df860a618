import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/group/presentation/widgets/group_filter_widget.dart';
import '../../../../core/app/constants/routes.dart';

import '../../controllers/group_controller.dart';
import '../../../../data/models/group_model.dart';

class GroupScreen extends GetView<GroupController> {
  const GroupScreen({super.key});

  static final Logger _logger = Get.find<Logger>();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final showFilter = false.obs;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Groups',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: GroupFilterWidget().buildSearchField(
                Get.find<GroupController>(),
                context,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt)
                      : Icon(Icons.filter_alt_outlined),
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.groups.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.groups.isEmpty && !controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(IconlyLight.document, size: 48),
                const SizedBox(height: 16),
                Text('No groups found', style: theme.textTheme.titleMedium),
                if (controller.searchQuery.isNotEmpty ||
                    controller.startDate.value != null ||
                    controller.endDate.value != null)
                  TextButton(
                    onPressed: () => controller.clearFilters(),
                    child: const Text('Clear filters'),
                  ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => controller.refreshGroups(),
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (scrollInfo.metrics.pixels ==
                      scrollInfo.metrics.maxScrollExtent &&
                  !controller.isLoading.value &&
                  !controller.isLastPage.value) {
                controller.loadMoreGroups();
              }
              return false;
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildGroupTabs(context),
                  const SizedBox(height: 24),
                  if (controller.groups.isNotEmpty &&
                      controller.searchQuery.isEmpty)
                    _buildFeaturedGroup(context),
                  const SizedBox(height: 24),
                  _buildSectionTitle('Groups'),
                  if (controller.searchQuery.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        children: [
                          Text(
                            'Search results for: "${controller.searchQuery.value}"',
                          ),
                          TextButton(
                            onPressed: () => controller.clearFilters(),
                            child: const Text('Clear'),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),
                  _buildGroupsList(context),
                  if (controller.isLoading.value &&
                      controller.groups.isNotEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                ],
              ),
            ),
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        backgroundColor: colorScheme.primary,
        child: const Icon(IconlyBold.plus, color: Colors.white),
        onPressed: () => context.go(Routes.CREATE_GROUP),
      ),
    );
  }

  Widget _buildGroupTabs(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'Groups',
                  style: TextStyle(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => Navigator.of(context).pushNamed(Routes.EVENTS),
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    'Events',
                    style: TextStyle(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildFeaturedGroup(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final featuredGroup = controller.groups.first;

    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
        image: const DecorationImage(
          image: AssetImage('assets/images/group_bg.jpg'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(Colors.black38, BlendMode.darken),
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 5,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'FEATURED',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  featuredGroup.title ?? 'Untitled Group',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  featuredGroup.description ?? '',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(
                      IconlyLight.user3,
                      color: Colors.white70,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${featuredGroup.members?.length ?? 0} members',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  debugPrint("Going to group details");
                  if (featuredGroup.id != null) {
                    context.go(
                      Routes.GROUP_DETAIL.replaceFirst(
                        ':id',
                        featuredGroup.id!,
                      ),
                      extra: featuredGroup,
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupsList(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: controller.groups.length,
      itemBuilder: (context, index) {
        final group = controller.groups[index];
        return _buildGroupCard(context, group);
      },
    );
  }

  Widget _buildGroupCard(BuildContext context, GroupModel group) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (group.id != null) {
            context.go(
              Routes.GROUP_DETAIL.replaceFirst(':id', group.id!),
              extra: group,
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        group.title?.substring(0, 1) ?? 'G',
                        style: TextStyle(
                          color: colorScheme.onPrimaryContainer,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          group.title ?? 'Untitled Group',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          group.description ?? '',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.textTheme.bodyMedium?.color
                                ?.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(IconlyLight.user3, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${group.members?.length ?? 0} members',
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Add edit button
                  IconButton(
                    onPressed: () {
                      if (group.id != null) {
                        _logger.d(
                          'GroupScreen: Navigating to edit group with ID: ${group.id}',
                        );
                        _logger.d('GroupScreen: Group title: ${group.title}');
                        _logger.d(
                          'GroupScreen: Group members count: ${group.members?.length ?? 0}',
                        );
                        context.go(
                          Routes.GROUP_EDIT.replaceFirst(':id', group.id!),
                          extra: group,
                        );
                      }
                    },
                    icon: const Icon(IconlyLight.edit, color: Colors.blue),
                    tooltip: 'Edit Group',
                  ),
                ],
              ),
              if (group.members != null && group.members!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Key Members',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 40,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount:
                              group.members!.length > 3
                                  ? 3
                                  : group.members!.length,
                          itemBuilder: (context, index) {
                            final member = group.members![index];
                            return Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Chip(
                                avatar: CircleAvatar(
                                  backgroundColor: colorScheme.primary,
                                  child: Text(
                                    member.member?.firstName?.substring(0, 1) ??
                                        'M',
                                    style: TextStyle(
                                      color: colorScheme.onPrimary,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                                label: Text(
                                  '${member.member?.firstName ?? ''} (${member.role ?? 'Member'})',
                                  style: theme.textTheme.bodySmall,
                                ),
                                backgroundColor: colorScheme.surfaceVariant,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
