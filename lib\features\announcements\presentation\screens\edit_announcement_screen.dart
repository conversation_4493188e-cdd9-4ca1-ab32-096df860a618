import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_autocomplete.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/features/media_upload/media_upload.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/announcement_controller.dart';
import '../../../../data/models/announcement_model.dart';

class EditAnnouncementScreen extends StatefulWidget {
  final String announcementId;
  final AnnouncementModel? initialAnnouncement;

  const EditAnnouncementScreen({
    super.key,
    required this.announcementId,
    this.initialAnnouncement,
  });

  @override
  State<EditAnnouncementScreen> createState() => _EditAnnouncementScreenState();
}

class _EditAnnouncementScreenState extends State<EditAnnouncementScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late AnnouncementController _controller;
  String _description = '';

  @override
  void initState() {
    super.initState();
    _controller = Get.find<AnnouncementController>();
    _controller.initEditAnnouncementWithData(
      widget.announcementId,
      widget.initialAnnouncement,
    );
  }

  void _saveAnnouncement() async {
    if (_formKey.currentState!.validate()) {
      if (_description.trim().isNotEmpty) {
        _controller.descriptionFormController.text = _description;
      }

      final success = await _controller.updateAnnouncement(
        widget.announcementId,
      );
      if (success) {
        context.go('${Routes.ANNOUNCEMENTS}/${widget.announcementId}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final AnnouncementController controller = Get.find();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Announcement'),
        actions: [
          Obx(
            () =>
                controller.isSubmitting.value
                    ? const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                    )
                    : TextButton.icon(
                      onPressed: _saveAnnouncement,
                      icon: const Icon(IconlyLight.tickSquare),
                      label: const Text('Save'),
                    ),
          ),
        ],
      ),
      body: Obx(() {
        final announcement = controller.announcement.value;
        final isLoading = controller.isLoading.value;

        if (isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (announcement == null) {
          // Navigate back if no announcement found
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.go(Routes.ANNOUNCEMENTS);
          });
          return const SizedBox.shrink();
        }

        return Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Announcement Form Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 20.h,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Edit Announcement",
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Gap(16.h),

                        // Title Field
                        Text(
                          "Announcement Title *",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomTextFormField(
                          controller: controller.titleFormController,
                          hintText: 'Enter announcement title',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                        // Category Field
                        Text(
                          "Category",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomAutocomplete(
                          options: controller.itemCategories,
                          controller: controller.categoryController,
                          hintText: 'Enter or select announcement category',
                          onSelected: (value) {},
                        ),
                        Gap(16.h),

                        // Username Field
                        Text(
                          "Username",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomTextFormField(
                          controller: controller.usernameController,
                          hintText: 'Enter username',
                        ),
                        Gap(16.h),

                        // Type Field
                        Text(
                          "Type",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomTextFormField(
                          controller: controller.typeController,
                          hintText: 'Enter announcement type',
                        ),
                        Gap(16.h),

                        // Location Name Field
                        Text(
                          "Location (Optional)",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomTextFormField(
                          controller: controller.locationNameController,
                          hintText: 'Enter location (e.g., church hall)',
                        ),
                        Gap(16.h),

                        // Description Field with Markup Editor
                        MarkupEditorWidget(
                          label: "Description",
                          isRequired: true,
                          hintText:
                              "Enter announcement description with rich formatting",
                          height: 250.h,
                          initialValue:
                              controller.descriptionFormController.text,
                          onChanged: (content) {
                            _description = content;
                          },
                        ),
                        Gap(16.h),

                        // Media Upload Section
                        Card(
                          elevation: 0,
                          color: Colors.grey.shade50,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.grey.shade300),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(IconlyLight.image),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Media Items (Optional)',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                                Gap(12.h),
                                MediaUploadWidget(
                                  category: 'ANNOUNCEMENT',
                                  multipleSelect: true,
                                  onMediaSelected: (selectedMedia) {
                                    controller.mediaItems.value = selectedMedia;
                                  },
                                ),
                                Gap(12.h),
                                Obx(
                                  () =>
                                      controller.mediaItems.isEmpty
                                          ? Center(
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                16.0,
                                              ),
                                              child: Text(
                                                'No media items added yet',
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                            ),
                                          )
                                          : ListView.builder(
                                            shrinkWrap: true,
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            itemCount:
                                                controller.mediaItems.length,
                                            itemBuilder: (context, index) {
                                              final item =
                                                  controller.mediaItems[index];
                                              return ListTile(
                                                contentPadding: EdgeInsets.zero,
                                                leading: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                  child: Image.network(
                                                    item.mediaUrl ?? '',
                                                    width: 40,
                                                    height: 40,
                                                    fit: BoxFit.cover,
                                                    errorBuilder:
                                                        (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) => Container(
                                                          width: 40,
                                                          height: 40,
                                                          color:
                                                              Colors
                                                                  .grey
                                                                  .shade300,
                                                          child: const Icon(
                                                            Icons.error,
                                                            size: 20,
                                                          ),
                                                        ),
                                                  ),
                                                ),
                                                title: Text(
                                                  item.title ?? 'Media Item',
                                                ),
                                                subtitle: Text(
                                                  item.mediaUrl ?? '',
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                trailing: IconButton(
                                                  icon: const Icon(
                                                    Icons.delete,
                                                    color: Colors.red,
                                                  ),
                                                  onPressed:
                                                      () => controller
                                                          .removeMediaItem(
                                                            index,
                                                          ),
                                                  tooltip: 'Remove',
                                                ),
                                              );
                                            },
                                          ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Gap(16.h),
              ],
            ),
          ),
        );
      }),
    );
  }
}
