import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import '../widgets/edit_category_form_widget.dart';

class EditCategoryScreen extends StatefulWidget {
  final String categoryId;
  final SubAccountCategory? initialCategory;

  const EditCategoryScreen({
    super.key,
    required this.categoryId,
    this.initialCategory,
  });

  @override
  State<EditCategoryScreen> createState() => _EditCategoryScreenState();
}

class _EditCategoryScreenState extends State<EditCategoryScreen> {
  final controller = Get.put(FinanceController());
  final Rx<SubAccountCategory?> category = Rx<SubAccountCategory?>(null);
  final RxBool isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    if (widget.initialCategory != null) {
      category.value = widget.initialCategory;
      _initializeForm();
    } else {
      _loadCategory();
    }
  }

  Future<void> _loadCategory() async {
    try {
      isLoading.value = true;

      // Find category in the controller's categories list
      final foundCategory = controller.categories.firstWhereOrNull(
        (cat) => cat.id == widget.categoryId,
      );

      if (foundCategory != null) {
        category.value = foundCategory;
        _initializeForm();
      } else {
        // If not found in current list, fetch all categories
        await controller.fetchCategories();
        final refetchedCategory = controller.categories.firstWhereOrNull(
          (cat) => cat.id == widget.categoryId,
        );
        category.value = refetchedCategory;
        if (refetchedCategory != null) {
          _initializeForm();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error loading category: $e');
      }
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeForm() {
    final cat = category.value;
    if (cat != null) {
      controller.titleController.text = cat.title ?? '';
      controller.descriptionController.text = cat.description ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Category'),
        leading: IconButton(
          icon: const Icon(IconlyLight.arrowLeft),
          onPressed: () {
            // Clear form when going back
            controller.clearForm();
            context.go(
              Routes.VIEW_ACCOUNT_CATEGORY.replaceFirst(
                ':id',
                widget.categoryId,
              ),
              extra: category.value,
            );
          },
        ),
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (category.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyLight.dangerCircle,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                Gap(16.h),
                Text(
                  'Category not found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                  ),
                ),
                Gap(8.h),
                Text(
                  'The category you are trying to edit does not exist',
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                ),
                Gap(24.h),
                ElevatedButton.icon(
                  onPressed: () => context.go(Routes.ACCOUNT_CATEGORIES),
                  icon: const Icon(IconlyLight.arrowLeft),
                  label: const Text('Back to Categories'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(8.h),

              // Form Section
              EditCategoryFormWidget(
                categoryId: widget.categoryId,
                category: category.value!,
              ),
            ],
          ),
        );
      }),
    );
  }

  @override
  void dispose() {
    // Clear form when disposing
    controller.clearForm();
    super.dispose();
  }
}
