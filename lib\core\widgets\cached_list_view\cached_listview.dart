// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:connectivity_plus/connectivity_plus.dart';



// class CachedListView<T> extends StatelessWidget {
//   final CachedListController<T> controller;
//   final Widget Function(BuildContext, T) itemBuilder;
//   final Widget? placeholder;
//   final Widget Function(String)? errorWidget;
//   final double cacheExtent;
//   final Duration transitionDuration;
//   final Curve transitionCurve;
//   final bool refreshIndicator;

//   const CachedListView({
//     Key? key,
//     required this.controller,
//     required this.itemBuilder,
//     this.placeholder,
//     this.errorWidget,
//     this.cacheExtent = 250.0,
//     this.transitionDuration = const Duration(milliseconds: 300),
//     this.transitionCurve = Curves.easeInOut,
//     this.refreshIndicator = true,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       return AnimatedSwitcher(
//         duration: transitionDuration,
//         switchInCurve: transitionCurve,
//         switchOutCurve: transitionCurve,
//         child: _buildContent(context),
//       );
//     });
//   }

//   Widget _buildContent(BuildContext context) {
//     if (controller.hasError.value && controller.items.isEmpty) {
//       return errorWidget?.call('Error loading data') ??
//           const Center(child: Text('Failed to load data'));
//     } else if (controller.isLoading.value && controller.items.isEmpty) {
//       return placeholder ?? const Center(child: CircularProgressIndicator());
//     } else {
//       final listView = ListView.builder(
//         controller: controller.scrollController,
//         cacheExtent: cacheExtent,
//         itemCount: controller.items.length + (controller.isLastPage.value ? 0 : 1),
//         itemBuilder: (context, index) {
//           if (index >= controller.items.length) {
//             return const Center(child: Padding(
//               padding: EdgeInsets.all(16.0),
//               child: CircularProgressIndicator(),
//             ));
//           }
//           return itemBuilder(context, controller.items[index]);
//         },
//       );

//       return refreshIndicator
//           ? RefreshIndicator(
//               onRefresh: () => controller.refresh(),
//               child: listView,
//             )
//           : listView;
//     }
//   }
// }

// class CachedListController<T> extends GetxController {
//   final Future<PageData<T>> Function(int page, int size) fetcher;
//   final String Function(int page) cacheKeyBuilder;
//   final Duration cacheTTL;
//   final int pageSize;
//   final int? maxItems;
//   final void Function(PageData<T>)? onPageFetched;
//   final void Function()? onEndReached;

//   final RxList<T> items = <T>[].obs;
//   final RxBool isLoading = false.obs;
//   final RxBool hasError = false.obs;
//   final RxBool isLastPage = false.obs;
//   final RxBool isConnected = true.obs;

//   final GetStorage _storage = GetStorage();
//   final Connectivity _connectivity = Connectivity();
//   final ScrollController scrollController = ScrollController();

//   int _currentPage = 0;
//   bool _isFetching = false;

//   CachedListController({
//     required this.fetcher,
//     required this.cacheKeyBuilder,
//     required this.cacheTTL,
//     required this.pageSize,
//     this.maxItems,
//     this.onPageFetched,
//     this.onEndReached,
//   }) {
//     scrollController.addListener(_scrollListener);
//     _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
//   }

//   @override
//   void onInit() async {
//     super.onInit();
//     await init();
//     _checkInitialConnection();
//   }

//   @override
//   void onClose() {
//     scrollController.dispose();
//     super.onClose();
//   }

//   Future<void> _checkInitialConnection() async {
//     final result = await _connectivity.checkConnectivity();
//     isConnected.value = result != ConnectivityResult.none;
//   }

//   void _updateConnectionStatus(ConnectivityResult result) {
//     isConnected.value = result != ConnectivityResult.none;
//     if (isConnected.value) _revalidateCache();
//   }

//   Future<void> _revalidateCache() async {
//     await refresh();
//   }

//   Future<void> init() async {
//     isLoading.value = true;
//     try {
//       await fetchPage(0);
//     } catch (e) {
//       hasError.value = true;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<void> fetchPage(int page) async {
//     if (_isFetching || isLastPage.value || (maxItems != null && items.length >= maxItems!)) return;

//     _isFetching = true;
//     isLoading.value = true;
//     hasError.value = false;

//     try {
//       final (cachedData, isExpired) = _loadPageFromCache(page);
//       if (cachedData != null) {
//         _updateItems(cachedData.items, page);
//         onPageFetched?.call(cachedData);
//         if (isConnected.value && isExpired) {
//           await _fetchFromNetwork(page);
//         }
//       } else if (isConnected.value) {
//         await _fetchFromNetwork(page);
//       } else {
//         throw Exception('No cache available and offline');
//       }
//     } catch (e) {
//       hasError.value = true;
//       if (items.isEmpty) rethrow;
//     } finally {
//       isLoading.value = false;
//       _isFetching = false;
//     }
//   }

//   Future<void> _fetchFromNetwork(int page) async {
//     final newData = await fetcher(page, pageSize);
//     _savePageToCache(newData);
//     _updateItems(newData.items, page);
//     onPageFetched?.call(newData);
//     isLastPage.value = newData.last;
//     _currentPage = newData.page;
//   }

//   void _updateItems(List<T> newItems, int page) {
//     if (page == 0) {
//       items.assignAll(newItems);
//     } else {
//       items.addAll(newItems);
//     }
//     if (maxItems != null && items.length > maxItems!) {
//       items.value = items.sublist(0, maxItems!);
//       isLastPage.value = true;
//     }
//   }

//   (PageData<T>?, bool) _loadPageFromCache(int page) {
//     final key = cacheKeyBuilder(page);
//     final cached = _storage.read(key);
//     if (cached == null) return (null, false);

//     final expiresAt = cached['expiresAt'] as int;
//     final isExpired = DateTime.now().millisecondsSinceEpoch > expiresAt;
//     final data = PageData<T>.fromMap(cached['data']);

//     return (data, isExpired);
//   }

//   void _savePageToCache(PageData<T> data) {
//     final key = cacheKeyBuilder(data.page);
//     final expiresAt = DateTime.now().add(cacheTTL).millisecondsSinceEpoch;
//     _storage.write(key, {
//       'expiresAt': expiresAt,
//       'data': data.toMap(),
//     });
//   }

//   Future<void> fetchNextPage() async {
//     if (isLoading.value || isLastPage.value) return;
//     await fetchPage(_currentPage + 1);
//   }

//   Future<void> refresh() async {
//     _currentPage = 0;
//     isLastPage.value = false;
//     items.clear();
//     await fetchPage(0);
//   }

//   void _scrollListener() {
//     if (scrollController.position.pixels >= scrollController.position.maxScrollExtent * 0.8) {
//       onEndReached?.call();
//       fetchNextPage();
//     }
//   }
// }
// class PageData<T> {
//   final int page;
//   final int size;
//   final int maxPage;
//   final int totalPages;
//   final int total;
//   final bool last;
//   final bool first;
//   final int visible;
//   final List<T> items;

//   PageData({
//     required this.page,
//     required this.size,
//     required this.maxPage,
//     required this.totalPages,
//     required this.total,
//     required this.last,
//     required this.first,
//     required this.visible,
//     required this.items,
//   });

//   Map<String, dynamic> toMap() {
//     return {
//       'page': page,
//       'size': size,
//       'maxPage': maxPage,
//       'totalPages': totalPages,
//       'total': total,
//       'last': last,
//       'first': first,
//       'visible': visible,
//       'items': items,
//     };
//   }

//   factory PageData.fromMap(Map<String, dynamic> map) {
//     return PageData<T>(
//       page: map['page'],
//       size: map['size'],
//       maxPage: map['maxPage'],
//       totalPages: map['totalPages'],
//       total: map['total'],
//       last: map['last'],
//       first: map['first'],
//       visible: map['visible'],
//       items: List<T>.from(map['items'] as List),
//     );
//   }
// }

// ///[Usage]
// /**
//  * // Initialize controller
// final controller = CachedListController<Post>(
//   fetcher: (page, size) => api.fetchPosts(page, size),
//   cacheKeyBuilder: (page) => 'posts_page_$page',
//   cacheTTL: Duration(minutes: 10),
//   pageSize: 10,
//   maxItems: 100,
//   onPageFetched: (data) => debugPrint('Fetched page ${data.page}'),
//   onEndReached: () => debugPrint('End reached'),
// );

// // Use in widget tree
// CachedListView<Post>(
//   controller: controller,
//   itemBuilder: (context, post) => PostCard(post: post),
//   placeholder: Center(child: CircularProgressIndicator()),
//   errorWidget: (error) => Center(child: Text('Error: $error')),
// );
//  */