import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/features/media_upload/presentation/widgets/media_upload_widget.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:onechurch/features/members/presentation/screens/members_screen/members_screen.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/constants/enums.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/app/widgets/custom_dropdown.dart';
import '../../../../core/app/widgets/custom_text_field.dart';
import '../../../group/controllers/group_controller.dart';
import '../../../../data/models/group_model.dart';

class EditGroupScreen extends StatefulWidget {
  final String groupId;
  final GroupModel group;

  const EditGroupScreen({
    super.key,
    required this.groupId,
    required this.group,
  });

  @override
  State<EditGroupScreen> createState() => _EditGroupScreenState();
}

class _EditGroupScreenState extends State<EditGroupScreen> {
  late GroupController controller;
  late MemberController memberController;
  late GlobalKey<FormState> _formKey;
  late List<String> statusOptions;
  late List<String> mediaTypeOptions;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    controller = Get.find<GroupController>();
    memberController = Get.find<MemberController>();
    _formKey = GlobalKey<FormState>();
    statusOptions = ['active', 'inactive', 'draft'];
    mediaTypeOptions = ['IMAGE', 'VIDEO', 'DOCUMENT'];

    // Set the group directly and load for editing
    controller.selectedGroup.value = widget.group;
    controller.loadGroupForEdit(widget.group);

    // Listen for changes to track unsaved changes
    _setupChangeListeners();
  }

  void _setupChangeListeners() {
    controller.titleController.addListener(() {
      if (mounted) {
        setState(() {
          _hasUnsavedChanges = true;
        });
      }
    });

    controller.descriptionController.addListener(() {
      if (mounted) {
        setState(() {
          _hasUnsavedChanges = true;
        });
      }
    });
  }

  @override
  void dispose() {
    // Clear any listeners if needed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return await _handleBackNavigation(context);
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Edit Group'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              if (await _handleBackNavigation(context)) {
                context.go(Routes.GROUPS);
              }
            },
          ),
        ),
        body: _buildEditForm(
          context,
          memberController,
          _formKey,
          statusOptions,
        ),
      ),
    );
  }

  Future<bool> _handleBackNavigation(BuildContext context) async {
    if (_hasUnsavedChanges) {
      final shouldLeave = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Unsaved Changes'),
              content: const Text(
                'You have unsaved changes. Are you sure you want to leave without saving?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Leave'),
                ),
              ],
            ),
      );
      return shouldLeave ?? false;
    }
    return true;
  }

  Widget _buildEditForm(
    BuildContext context,
    MemberController memberController,
    GlobalKey<FormState> formKey,
    List<String> statusOptions,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic Information Section
            const Text(
              'Basic Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Gap(16),
            CustomTextField(
              label: 'Group Title',
              controller: controller.titleController,
              hintText: 'Enter group title',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const Gap(16),
            CustomTextField(
              label: 'Description',
              controller: controller.descriptionController,
              hintText: 'Enter group description',
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
            const Gap(16),
            Obx(
              () => CustomDropdown(
                labelText: 'Status',
                value: controller.statusValue.value,
                items:
                    statusOptions.map((String status) {
                      return DropdownMenuItem<String>(
                        value: status,
                        child: Text(status.capitalize),
                      );
                    }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    controller.statusValue.value = newValue;
                    setState(() {
                      _hasUnsavedChanges = true;
                    });
                  }
                },
              ),
            ),

            const Gap(24),

            // Media Section
            _buildMediaSection(),

            const Gap(24),

            // Members Section
            _buildMembersSection(memberController, context),

            const Gap(32),

            Obx(() {
              if (controller.errorMessage.isNotEmpty) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Text(
                    controller.errorMessage.value,
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            Center(
              child: SizedBox(
                width: 200,
                child: Obx(
                  () => CustomButton(
                    onPressed:
                        controller.isSubmitting.value
                            ? () {}
                            : () => _handleSubmit(context, formKey),
                    text: 'Update Group',
                    isLoading: controller.isSubmitting.value,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Media',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const Gap(16),
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MediaUploadWidget(
                  category: 'GROUPS',
                  multipleSelect: true,
                  onMediaSelected: (selectedMedia) {
                    controller.mediaItems.addAll(selectedMedia);
                    setState(() {
                      _hasUnsavedChanges = true;
                    });
                  },
                ),

                const Gap(16),

                Obx(() {
                  if (controller.mediaItems.isNotEmpty) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Added Media:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Gap(8),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: controller.mediaItems.length,
                          itemBuilder: (context, index) {
                            final media = controller.mediaItems[index];
                            return ListTile(
                              title: Text(media.title ?? 'Media Item'),
                              subtitle: Text(media.mediaUrl ?? ''),
                              leading: Icon(
                                media.type == 'IMAGE'
                                    ? Icons.image
                                    : media.type == 'VIDEO'
                                    ? Icons.video_library
                                    : Icons.insert_drive_file,
                              ),
                              trailing: IconButton(
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                ),
                                onPressed: () {
                                  controller.removeMediaItem(index);
                                  setState(() {
                                    _hasUnsavedChanges = true;
                                  });
                                },
                              ),
                            );
                          },
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMembersSection(
    MemberController memberController,
    BuildContext context,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Members',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const Gap(16),
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Group Members',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    CircleAvatar(
                      child: IconButton(
                        onPressed: () async {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder:
                                  (context) => MemberViewScreen(isSelect: true),
                            ),
                          );
                          // context.go(
                          //   Routes.MEMBERS,
                          //   extra: {'isSelect': true},
                          // );
                        },
                        icon: const Icon(IconlyLight.addUser),
                      ),
                    ),
                  ],
                ),
                const Gap(16),
                // Always show members section, whether empty or not
                const Text(
                  'Current Members:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Gap(8),
                Obx(() {
                  if (memberController.selectedMembers.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Text(
                          'No members added yet. Click the + button to add members.',
                          style: TextStyle(
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: memberController.selectedMembers.length,
                    itemBuilder: (context, index) {
                      final member = memberController.selectedMembers[index];

                      // Find the corresponding group member data
                      final groupMember = controller.selectedMembers
                          .firstWhereOrNull((m) => m['member_id'] == member.id);

                      final currentRole =
                          groupMember?['role'] ??
                          GroupMemberRole.member.displayName;

                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            child: Text(
                              (member.firstName?.isNotEmpty == true
                                      ? member.firstName!.substring(0, 1)
                                      : 'M')
                                  .toUpperCase(),
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(
                            "${member.firstName ?? ''} ${member.secondName ?? ''}"
                                .trim(),
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Role: $currentRole'),
                              if (member.phoneNumber?.isNotEmpty == true)
                                Text(
                                  'Phone: ${member.phoneNumber}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(
                                  Icons.edit,
                                  color: Colors.blue,
                                ),
                                onPressed: () async {
                                  await _showRoleSelectionDialog(
                                    context,
                                    member.id ?? '',
                                    currentRole,
                                  ).whenComplete(() => setState(() {}));
                                },
                                tooltip: 'Edit Role',
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                ),
                                onPressed: () {
                                  controller.removeMember(member.id ?? '');
                                  memberController.selectedMembers.removeWhere(
                                    (m) => m.id == member.id,
                                  );
                                  setState(() {
                                    _hasUnsavedChanges = true;
                                  });
                                },
                                tooltip: 'Remove Member',
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handleSubmit(BuildContext context, GlobalKey<FormState> formKey) async {
    if (formKey.currentState!.validate()) {
      // Check if at least one admin exists
      if (!_hasAtLeastOneAdmin()) {
        _showAdminValidationError(context);
        return;
      }

      try {
        final success = await controller.updateGroup(widget.groupId);
        if (success) {
          // Mark changes as saved
          setState(() {
            _hasUnsavedChanges = false;
          });

          // Refresh the groups list to reflect the changes
          await controller.refreshGroups();

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Group updated successfully'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );

            // Navigate back to groups list
            context.go(Routes.GROUPS);
          }
        } else {
          // Show error message if update failed
          if (context.mounted && controller.errorMessage.isNotEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(controller.errorMessage.value),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } catch (e) {
        // Handle any unexpected errors
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('An error occurred: ${e.toString()}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  // Show role selection dialog
  Future<void> _showRoleSelectionDialog(
    BuildContext context,
    String memberId,
    String currentRole,
  ) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return _RoleSelectionDialog(
          memberId: memberId,
          currentRole: currentRole,
          onRoleSelected: (String role) {
            controller.addMember(memberId, role);
            setState(() {
              _hasUnsavedChanges = true;
            });
          },
        );
      },
    );
  }

  // Check if at least one admin exists
  bool _hasAtLeastOneAdmin() {
    return controller.selectedMembers.any(
      (member) => member['role'] == GroupMemberRole.admin.displayName,
    );
  }

  // Show admin validation error
  void _showAdminValidationError(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('At least one member must be assigned as an admin'),
        backgroundColor: Colors.red,
      ),
    );
  }
}

// Role Selection Dialog Widget
class _RoleSelectionDialog extends StatefulWidget {
  final String memberId;
  final String currentRole;
  final Function(String) onRoleSelected;

  const _RoleSelectionDialog({
    required this.memberId,
    required this.currentRole,
    required this.onRoleSelected,
  });

  @override
  State<_RoleSelectionDialog> createState() => _RoleSelectionDialogState();
}

class _RoleSelectionDialogState extends State<_RoleSelectionDialog> {
  late String selectedRole;

  @override
  void initState() {
    super.initState();
    selectedRole = widget.currentRole;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Role'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            GroupMemberRole.values.map((role) {
              return RadioListTile<String>(
                title: Text(role.displayName),
                value: role.displayName,
                groupValue: selectedRole,
                onChanged: (String? value) {
                  if (value != null) {
                    setState(() {
                      selectedRole = value;
                    });
                  }
                },
              );
            }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            widget.onRoleSelected(selectedRole);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
