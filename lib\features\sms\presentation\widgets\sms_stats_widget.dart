import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import '../../controllers/sms_controller.dart';

class SmsStatsWidget extends StatelessWidget {
  const SmsStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.chart, color: colorScheme.primary, size: 20),
                Gap(8.w),
                Text(
                  'SMS Statistics',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Obx(
                  () =>
                      controller.isLoading.value
                          ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: colorScheme.primary,
                            ),
                          )
                          : Icon(
                            IconlyLight.infoSquare,
                            size: 16,
                            color: colorScheme.onSurfaceVariant,
                          ),
                ),
              ],
            ),
            Gap(16.h),
            _buildStatsGrid(context, controller),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(BuildContext context, SmsController controller) {
    return Obx(() {
      // Calculate stats from messages
      final totalMessages = controller.totalItems.value;
      final deliveredCount =
          controller.messages
              .where((msg) => msg.status?.toLowerCase() == 'delivered')
              .length;
      final pendingCount =
          controller.messages
              .where((msg) => msg.status?.toLowerCase() == 'pending')
              .length;
      final failedCount =
          controller.messages
              .where(
                (msg) =>
                    msg.status?.toLowerCase() == 'failed' ||
                    msg.status?.toLowerCase() == 'blocked',
              )
              .length;

      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Messages',
                  totalMessages.toString(),
                  IconlyLight.message,
                  Colors.blue,
                ),
              ),
              Gap(12.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Delivered',
                  deliveredCount.toString(),
                  IconlyLight.tickSquare,
                  Colors.green,
                ),
              ),
            ],
          ),
          Gap(12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Pending',
                  pendingCount.toString(),
                  IconlyLight.timeCircle,
                  Colors.orange,
                ),
              ),
              Gap(12.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Failed',
                  failedCount.toString(),
                  IconlyLight.closeSquare,
                  Colors.red,
                ),
              ),
            ],
          ),
          Gap(12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Current Page',
                  '${controller.currentPage.value + 1}',
                  IconlyLight.document,
                  Colors.purple,
                ),
              ),
              Gap(12.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Pages',
                  controller.totalPages.value.toString(),
                  IconlyLight.paper,
                  Colors.indigo,
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const Spacer(),
              Text(
                value,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          Gap(4.h),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 11,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
