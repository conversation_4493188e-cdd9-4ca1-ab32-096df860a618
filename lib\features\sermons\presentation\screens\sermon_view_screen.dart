import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:share_plus/share_plus.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../data/models/sermon_model.dart';
import '../../controllers/sermon_controller.dart';

class SermonViewScreen extends StatefulWidget {
  final String? sermonId;
  final SermonModel? initialSermon;
  const SermonViewScreen({super.key, this.sermonId, this.initialSermon});

  @override
  State<SermonViewScreen> createState() => _SermonViewScreenState();
}

class _SermonViewScreenState extends State<SermonViewScreen> {
  final SermonController controller = Get.find<SermonController>();

  SermonModel? _sermon;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadSermon();
  }

  Future<void> _loadSermon() async {
    // If we have an initial sermon, use it immediately
    if (widget.initialSermon != null) {
      setState(() {
        _sermon = widget.initialSermon;
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = true;
      });
    }

    // Always fetch the latest data from the API if we have an ID
    final String sermonId = widget.sermonId ?? '';
    if (sermonId.isNotEmpty) {
      try {
        final sermon = await controller.getSermonById(sermonId);
        if (sermon == null) {
          throw Exception('Sermon not found');
        }
        setState(() {
          _sermon = sermon;
          _isLoading = false;
        });
      } catch (_) {
        setState(() {
          _hasError =
              widget.initialSermon ==
              null; // Only show error if we don't have initial data
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: colorScheme.surface,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_hasError || _sermon == null) {
      return _buildErrorState(context);
    }

    final sermon = _sermon!;
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context, sermon),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSermonHeader(context, sermon),
                  const SizedBox(height: 24),
                  _buildActionButtons(context, sermon),
                  const SizedBox(height: 24),
                  _buildSermonContent(context, sermon),
                  const SizedBox(height: 32),
                  if (sermon.createdByUser != null)
                    _buildAuthorInfo(context, sermon),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildPlayButton(context),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Sermon',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(IconlyLight.dangerCircle, size: 48),
            const SizedBox(height: 16),
            Text('Sermon not found', style: theme.textTheme.titleMedium),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(BuildContext context, SermonModel sermon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SliverAppBar(
      expandedHeight: 240.0.h,
      pinned: true,
      leading:
          Navigator.canPop(context)
              ? IconButton(
                icon: Icon(IconlyLight.arrowLeft2, color: colorScheme.primary),
                onPressed: () => Navigator.of(context).pop(),
              )
              : null,
      flexibleSpace: FlexibleSpaceBar(
        background:
            sermon.media != null && sermon.media!.isNotEmpty
                ? Image.network(
                  sermon.media!.first.mediaUrl ?? '',
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) => Container(
                        color: colorScheme.primaryContainer,
                        child: const Center(
                          child: Icon(
                            IconlyBold.document,
                            color: Colors.white,
                            size: 48,
                          ),
                        ),
                      ),
                )
                : Container(
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    image: const DecorationImage(
                      image: AssetImage('assets/images/sermon_bg.jpg'),
                      fit: BoxFit.cover,
                      colorFilter: ColorFilter.mode(
                        Colors.black38,
                        BlendMode.darken,
                      ),
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      IconlyBold.document,
                      color: Colors.white,
                      size: 48,
                    ),
                  ),
                ),
      ),
      title: Text(
        'Sermon',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(IconlyLight.send, color: Colors.white),
          onPressed: () => _shareSermon(sermon),
        ),
        IconButton(
          icon: const Icon(IconlyLight.bookmark, color: Colors.white),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Sermon saved to bookmarks')),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSermonHeader(BuildContext context, SermonModel sermon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sermon title
        Text(
          sermon.title ?? 'Untitled Sermon',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // Preacher name
        if (sermon.createdByUser != null)
          Text(
            '${sermon.createdByUser?.firstName ?? ''} ${sermon.createdByUser?.secondName ?? ''}',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
            ),
          ),
        const SizedBox(height: 8),

        // Category (if available)
        if (sermon.category != null && sermon.category!.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              sermon.category!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Date and duration
        Row(
          children: [
            Icon(
              IconlyLight.calendar,
              size: 16,
              color: colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(width: 4),
            Text(
              _formatDate(sermon.createdAt),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(width: 16),
            Icon(
              IconlyLight.timeCircle,
              size: 16,
              color: colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(width: 4),
            Text(
              '28 min', // This would ideally come from the sermon model
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, SermonModel sermon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          context,
          icon: IconlyLight.download,
          label: 'Download',
          onTap: () {
            if (sermon.media != null && sermon.media!.isNotEmpty) {
              final mediaUrl = sermon.media!.first.mediaUrl ?? '';
              if (mediaUrl.isNotEmpty) {
                launchUrl(Uri.parse(mediaUrl));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Downloading sermon...')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('No valid media URL found')),
                );
              }
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('No media available to download')),
              );
            }
          },
        ),
        _buildActionButton(
          context,
          icon: IconlyLight.send,
          label: 'Share',
          onTap: () => _shareSermon(sermon),
        ),
        _buildActionButton(
          context,
          icon: IconlyLight.bookmark,
          label: 'Save',
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Sermon saved to bookmarks')),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceVariant,
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: colorScheme.primary),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSermonContent(BuildContext context, SermonModel sermon) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sermon Content',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (sermon.description != null && sermon.description!.isNotEmpty)
          RichText(text: HTML.toTextSpan(context, sermon.description!))
        else
          Text(
            'No content available for this sermon.',
            style: theme.textTheme.bodyLarge,
          ),
      ],
    );
  }

  Widget _buildAuthorInfo(BuildContext context, SermonModel sermon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final user = sermon.createdByUser!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About the Preacher',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 32,
                backgroundColor: colorScheme.primary,
                backgroundImage:
                    user.profileUrl != null && user.profileUrl!.isNotEmpty
                        ? NetworkImage(user.profileUrl!)
                        : null,
                child:
                    user.profileUrl == null || user.profileUrl!.isEmpty
                        ? Text(
                          '${user.firstName?[0] ?? ''}${user.secondName?[0] ?? ''}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${user.firstName ?? ''} ${user.secondName ?? ''}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user.role ?? 'Preacher',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    if (user.email != null && user.email!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            IconlyLight.message,
                            size: 16,
                            color: colorScheme.onSurface.withOpacity(0.7),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            user.email!,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPlayButton(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return FloatingActionButton.extended(
      onPressed: () {
        final String sermonId = widget.sermonId ?? '';
        controller.getSermonById(sermonId).then((sermon) {
          if (sermon != null && sermon.media != null) {
            final mediaUrl = sermon.media.toString();
            if (mediaUrl.isNotEmpty) {
              launchUrl(Uri.parse(mediaUrl));
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No media available for this sermon'),
                ),
              );
            }
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('No media available for this sermon'),
              ),
            );
          }
        });
      },
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      icon: const Icon(IconlyBold.play),
      label: const Text('Play Sermon'),
    );
  }

  void _shareSermon(SermonModel sermon) {
    Share.share(
      'Check out this sermon: ${sermon.title}\n\nFrom: ${sermon.createdByUser?.firstName ?? ''} ${sermon.createdByUser?.secondName ?? ''}',
    );
  }

  // Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown date';

    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
