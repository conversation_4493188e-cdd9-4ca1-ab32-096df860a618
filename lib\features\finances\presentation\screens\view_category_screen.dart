import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';

class ViewCategoryScreen extends StatefulWidget {
  final String categoryId;
  final SubAccountCategory? initialCategory;

  const ViewCategoryScreen({
    super.key,
    required this.categoryId,
    this.initialCategory,
  });

  @override
  State<ViewCategoryScreen> createState() => _ViewCategoryScreenState();
}

class _ViewCategoryScreenState extends State<ViewCategoryScreen> {
  final controller = Get.put(FinanceController());
  final Rx<SubAccountCategory?> category = Rx<SubAccountCategory?>(null);
  final RxBool isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    if (widget.initialCategory != null) {
      category.value = widget.initialCategory;
    } else {
      _loadCategory();
    }
  }

  Future<void> _loadCategory() async {
    try {
      isLoading.value = true;

      // Find category in the controller's categories list
      final foundCategory = controller.categories.firstWhereOrNull(
        (cat) => cat.id == widget.categoryId,
      );

      if (foundCategory != null) {
        category.value = foundCategory;
      } else {
        // If not found in current list, fetch all categories
        await controller.fetchCategories();
        final refetchedCategory = controller.categories.firstWhereOrNull(
          (cat) => cat.id == widget.categoryId,
        );
        category.value = refetchedCategory;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error loading category: $e');
      }
    } finally {
      isLoading.value = false;
    }
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child: Icon(icon, size: 20),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(bool? isGeneral) {
    final isGeneralCategory = isGeneral ?? false;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.category, size: 16),
          SizedBox(width: 6.w),
          Text(
            isGeneralCategory ? 'General' : 'Organization',
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Category Details'),
        leading: IconButton(
          icon: const Icon(IconlyLight.arrowLeft),
          onPressed: () => context.go(Routes.ACCOUNT_CATEGORIES),
        ),
        actions: [
          Obx(() {
            if (category.value != null) {
              return IconButton(
                icon: const Icon(IconlyLight.edit),
                onPressed: () {
                  context.go(
                    Routes.EDIT_ACCOUNT_CATEGORY.replaceFirst(
                      ':id',
                      widget.categoryId,
                    ),
                    extra: category.value,
                  );
                },
                tooltip: 'Edit Category',
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (category.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(IconlyLight.dangerCircle, size: 64),
                Gap(16.h),
                Text(
                  'Category not found',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                ),
                Gap(8.h),
                Text(
                  'The category you are looking for does not exist',
                  style: TextStyle(fontSize: 14),
                ),
                Gap(24.h),
                ElevatedButton.icon(
                  onPressed: () => context.go(Routes.ACCOUNT_CATEGORIES),
                  icon: const Icon(IconlyLight.arrowLeft),
                  label: const Text('Back to Categories'),
                ),
              ],
            ),
          );
        }

        final cat = category.value!;

        return SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category Header Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(IconlyBold.category, size: 32),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  cat.title ?? 'Untitled Category',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                _buildStatusBadge(cat.isGeneral),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (cat.description != null &&
                          cat.description!.isNotEmpty) ...[
                        Gap(16.h),
                        Text(
                          'Description',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Gap(8.h),
                        Text(
                          cat.description!,
                          style: TextStyle(fontSize: 14, height: 1.5),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              Gap(16.h),

              // Category Details Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(16.h),
                      _buildDetailRow(
                        'Category ID',
                        cat.id ?? 'N/A',
                        IconlyLight.document,
                      ),
                      _buildDetailRow(
                        'Title',
                        cat.title ?? 'N/A',
                        IconlyLight.category,
                      ),
                      _buildDetailRow(
                        'Description',
                        cat.description ?? 'No description provided',
                        IconlyLight.document,
                      ),
                      _buildDetailRow(
                        'Type',
                        cat.isGeneral == true
                            ? 'General Category'
                            : 'Organization Category',
                        Icons.category,
                      ),
                      if (cat.createdAt != null)
                        _buildDetailRow(
                          'Created Date',
                          DateFormat(
                            'dd MMM yyyy, hh:mm a',
                          ).format(cat.createdAt!),
                          IconlyLight.calendar,
                        ),
                      if (cat.updatedAt != null)
                        _buildDetailRow(
                          'Last Updated',
                          DateFormat(
                            'dd MMM yyyy, hh:mm a',
                          ).format(cat.updatedAt!),
                          IconlyLight.timeCircle,
                        ),
                    ],
                  ),
                ),
              ),

              Gap(24.h),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => context.go(Routes.ACCOUNT_CATEGORIES),
                      icon: const Icon(IconlyLight.arrowLeft),
                      label: const Text('Back to Categories'),
                    ),
                  ),
                  Gap(16.w),
                  Expanded(
                    child: FilledButton.icon(
                      onPressed: () {
                        context.go(
                          Routes.EDIT_ACCOUNT_CATEGORY.replaceFirst(
                            ':id',
                            widget.categoryId,
                          ),
                          extra: cat,
                        );
                      },
                      icon: const Icon(IconlyLight.edit),
                      label: const Text('Edit Category'),
                    ),
                  ),
                ],
              ),
              Gap(24.h),
            ],
          ),
        );
      }),
    );
  }
}
