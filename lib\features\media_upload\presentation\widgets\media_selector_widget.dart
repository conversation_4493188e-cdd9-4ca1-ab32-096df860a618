import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:drag_and_drop_flutter/drag_and_drop_flutter.dart';
import 'package:logger/logger.dart';
import '../../controller/media_controller.dart';
import '../../models/media_model.dart';
// ignore: unused_import
import 'dart:html' as html if (dart.library.html) 'dart:html';

class MediaSelectorWidget extends StatefulWidget {
  final bool multipleSelect;
  final Function(List<MediaModel>) onMediaSelected;
  final double width;
  final double height;
  final String category;

  const MediaSelectorWidget({
    super.key,
    this.multipleSelect = false,
    required this.onMediaSelected,
    this.width = 100.0,
    this.height = 100.0,
    required this.category,
  });

  @override
  State<MediaSelectorWidget> createState() => MediaSelectorWidgetState();
}

class MediaSelectorWidgetState extends State<MediaSelectorWidget> {
  final ImagePicker _picker = ImagePicker();
  bool _isDragging = false;
  final Logger _logger = Get.find<Logger>();

  /// Enhanced drag and drop handler that processes dropped files
  Future<void> _handleDrop(DragData data) async {
    setState(() => _isDragging = false);

    try {
      _logger.d('Drag and drop: Processing ${data.items.length} items');

      if (data.items.isEmpty) {
        _logger.w('Drag and drop: No items found in drop data');
        return;
      }

      // Get the media controller
      final MediaController controller =
          Get.isRegistered<MediaController>()
              ? Get.find<MediaController>()
              : Get.put(MediaController());

      // Set the category for proper filename formatting
      controller.selectedCategory.value = widget.category;

      // Show loading indicator
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Processing dropped files...'),
                  ],
                ),
              ),
        );
      }

      final List<XFile> validFiles = [];

      // Process each dropped item
      for (final item in data.items) {
        try {
          // Log the item structure for debugging
          _logger.d(
            'Drag and drop: Item type: ${item.type}, data type: ${item.data.runtimeType}',
          );

          if (item.data != null) {
            // Get filename from the item (check different possible properties)
            String fileName = 'unknown_file';
            if (item.type.isNotEmpty) {
              // Try to extract filename from type or use a default based on type
              if (item.type.startsWith('image/')) {
                fileName =
                    'dropped_image_${DateTime.now().millisecondsSinceEpoch}.${_getExtensionFromMimeType(item.type)}';
              } else if (item.type.startsWith('video/')) {
                fileName =
                    'dropped_video_${DateTime.now().millisecondsSinceEpoch}.${_getExtensionFromMimeType(item.type)}';
              } else if (item.type.startsWith('audio/')) {
                fileName =
                    'dropped_audio_${DateTime.now().millisecondsSinceEpoch}.${_getExtensionFromMimeType(item.type)}';
              } else {
                fileName =
                    'dropped_file_${DateTime.now().millisecondsSinceEpoch}.${_getExtensionFromMimeType(item.type)}';
              }
            }

            final fileExtension = fileName.split('.').last.toLowerCase();

            // Validate file type using the same logic as MediaController
            if (_isValidFileType(fileExtension)) {
              // Handle different data types that might come from drag and drop
              try {
                Uint8List? fileBytes;

                if (item.data is String) {
                  // Try to decode if it's base64 data
                  final dataString = item.data as String;
                  if (dataString.startsWith('data:')) {
                    // Handle data URL format: data:image/jpeg;base64,/9j/4AAQ...
                    try {
                      final base64Data = dataString.split(',').last;
                      fileBytes = base64Decode(base64Data);
                      _logger.d('Drag and drop: Decoded base64 data URL');
                    } catch (e) {
                      _logger.e(
                        'Drag and drop: Failed to decode base64 data URL: $e',
                      );
                      continue;
                    }
                  } else {
                    // Try direct base64 decode
                    try {
                      fileBytes = base64Decode(dataString);
                      _logger.d('Drag and drop: Decoded base64 string');
                    } catch (e) {
                      _logger.w(
                        'Drag and drop: String data is not base64, skipping: $e',
                      );
                      continue;
                    }
                  }
                } else if (item.data is Uint8List) {
                  // Direct binary data
                  fileBytes = item.data as Uint8List;
                  _logger.d('Drag and drop: Using direct binary data');
                } else {
                  _logger.w(
                    'Drag and drop: Unsupported data type: ${item.data.runtimeType}',
                  );
                  continue;
                }

                if (fileBytes.isEmpty) {
                  _logger.w('Drag and drop: No valid file data found');
                  continue;
                }

                // Create XFile from binary data
                final xFile = XFile.fromData(
                  fileBytes,
                  name: fileName,
                  mimeType: item.type,
                );

                validFiles.add(xFile);
                _logger.d('Drag and drop: Added valid file: $fileName');
              } catch (e) {
                _logger.e('Drag and drop: Error creating XFile: $e');
                continue;
              }
            } else {
              _logger.w('Drag and drop: Skipped invalid file type: $fileName');
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Skipped unsupported file: $fileName'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            }
          }
        } catch (e) {
          _logger.e('Drag and drop: Error processing item: $e');
        }
      }

      // Upload valid files
      if (validFiles.isNotEmpty) {
        _logger.d('Drag and drop: Uploading ${validFiles.length} valid files');

        if (validFiles.length == 1) {
          await controller.uploadSingleMedia(validFiles.first);
        } else {
          await controller.uploadMultipleMedia(validFiles);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully uploaded ${validFiles.length} file(s)',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _logger.w('Drag and drop: No valid files found');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No valid media files found in drop'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Close loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    } catch (e) {
      _logger.e('Drag and drop: Error handling drop: $e');

      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing dropped files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if the file extension is valid for media upload
  bool _isValidFileType(String extension) {
    const validExtensions = {
      // Images
      'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg',
      // Videos
      'mp4', 'mov', 'avi', 'wmv', 'flv', 'webm',
      // Audio
      'mp3', 'wav', 'ogg', 'aac', 'm4a',
      // Documents
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt',
    };
    return validExtensions.contains(extension);
  }

  /// Get file extension from MIME type
  String _getExtensionFromMimeType(String mimeType) {
    switch (mimeType) {
      // Images
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      case 'image/gif':
        return 'gif';
      case 'image/webp':
        return 'webp';
      case 'image/bmp':
        return 'bmp';
      case 'image/svg+xml':
        return 'svg';

      // Videos
      case 'video/mp4':
        return 'mp4';
      case 'video/quicktime':
        return 'mov';
      case 'video/x-msvideo':
        return 'avi';
      case 'video/x-ms-wmv':
        return 'wmv';
      case 'video/x-flv':
        return 'flv';
      case 'video/webm':
        return 'webm';

      // Audio
      case 'audio/mpeg':
        return 'mp3';
      case 'audio/wav':
        return 'wav';
      case 'audio/ogg':
        return 'ogg';
      case 'audio/aac':
        return 'aac';
      case 'audio/mp4':
        return 'm4a';

      // Documents
      case 'application/pdf':
        return 'pdf';
      case 'application/msword':
        return 'doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'docx';
      case 'application/vnd.ms-excel':
        return 'xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return 'xlsx';
      case 'application/vnd.ms-powerpoint':
        return 'ppt';
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return 'pptx';
      case 'text/plain':
        return 'txt';

      default:
        return 'bin'; // Binary file extension as fallback
    }
  }

  @override
  Widget build(BuildContext context) {
    return DragDropArea(
      onDrop: _handleDrop,
      onDragEnter: (_) => setState(() => _isDragging = true),
      onDragExit: () => setState(() => _isDragging = false),
      child: GestureDetector(
        onTap: () => showMediaOptions(context),
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  _isDragging
                      ? Theme.of(context).primaryColor
                      : Colors.grey[400]!,
              width: _isDragging ? 2.0 : 1.0,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyBold.image,
                  size: widget.width > 100 ? 32 : 24,
                  color: Theme.of(context).primaryColor.withOpacity(0.8),
                ),
                SizedBox(height: widget.width > 100 ? 8 : 4),
                Text(
                  'Select Media',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: widget.width > 100 ? 14 : 12,
                    color: Theme.of(context).primaryColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void showMediaOptions(BuildContext context) {
    final MediaController mediaController =
        Get.isRegistered<MediaController>()
            ? Get.find<MediaController>()
            : Get.put(MediaController());

    mediaController.fetchMedia(refresh: true);

    final bool isSmallScreen = MediaQuery.of(context).size.width < 600;

    if (isSmallScreen) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => buildMediaSelectorDialog(
              context,
              mediaController,
              isSmallScreen,
            ),
      );
    } else {
      showDialog(
        context: context,
        builder:
            (context) => Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: buildMediaSelectorDialog(
                context,
                mediaController,
                isSmallScreen,
              ),
            ),
      );
    }
  }

  Widget buildMediaSelectorDialog(
    BuildContext context,
    MediaController controller,
    bool isSmallScreen,
  ) {
    final double dialogHeight =
        isSmallScreen
            ? MediaQuery.of(context).size.height * 0.8
            : MediaQuery.of(context).size.height * 0.7;
    final double dialogWidth =
        isSmallScreen
            ? MediaQuery.of(context).size.width
            : MediaQuery.of(context).size.width * 0.8;

    if (isSmallScreen) {
      return Container(
        height: dialogHeight,
        width: dialogWidth,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Media',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Upload Media',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).primaryColor.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildUploadOption(
                    context,
                    icon: IconlyBold.image,
                    title: 'Choose from Gallery',
                    onTap:
                        () => _pickImage(
                          ImageSource.gallery,
                          controller,
                          widget.category,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildUploadOption(
                    context,
                    icon: IconlyBold.camera,
                    title: 'Take a Photo',
                    onTap:
                        () => _pickImage(
                          ImageSource.camera,
                          controller,
                          widget.category,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.3),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: _buildDragDropArea(context, controller),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),

            Expanded(child: _buildMediaGallery(context, controller)),

            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  FilledButton.icon(
                    onPressed: () => Navigator.pop(context),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.grey.shade800,
                    ),
                    icon: Icon(IconlyLight.closeSquare),
                    label: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  FilledButton.icon(
                    onPressed: () {
                      if (controller.selectedMedia.isNotEmpty) {
                        Navigator.pop(context);
                        _navigateToConfirmationScreen(
                          context,
                          controller.selectedMedia,
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Please select at least one media item',
                            ),
                          ),
                        );
                      }
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    icon: const Icon(IconlyLight.tickSquare),
                    label: const Text('Select'),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        height: dialogHeight,
        width: dialogWidth,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Select Media',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(height: 1),

            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 250,
                    padding: const EdgeInsets.only(right: 16.0, top: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Upload Media',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            color: Theme.of(
                              context,
                            ).primaryColor.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildUploadOption(
                          context,
                          icon: Icons.photo_library,
                          title: 'Choose from Gallery',
                          onTap:
                              () => _pickImage(
                                ImageSource.gallery,
                                controller,
                                widget.category,
                              ),
                        ),
                        const SizedBox(height: 16),
                        _buildUploadOption(
                          context,
                          icon: Icons.camera_alt,
                          title: 'Take a Photo',
                          onTap:
                              () => _pickImage(
                                ImageSource.camera,
                                controller,
                                widget.category,
                              ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          height: 150,
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColor.withOpacity(0.05),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(
                                context,
                              ).primaryColor.withOpacity(0.3),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: _buildDragDropArea(context, controller),
                        ),
                      ],
                    ),
                  ),

                  const VerticalDivider(width: 1),

                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0, top: 16.0),
                      child: _buildMediaGallery(context, controller),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  FilledButton.icon(
                    onPressed: () => Navigator.pop(context),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.grey.shade800,
                    ),
                    icon: const Icon(IconlyLight.closeSquare),
                    label: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  FilledButton.icon(
                    onPressed: () {
                      if (controller.selectedMedia.isNotEmpty) {
                        Navigator.pop(context);
                        _navigateToConfirmationScreen(
                          context,
                          controller.selectedMedia,
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Please select at least one media item',
                            ),
                          ),
                        );
                      }
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    icon: const Icon(IconlyLight.tickSquare),
                    label: const Text('Select'),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildMediaGallery(BuildContext context, MediaController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.mediaItems.isEmpty) {
        return const Center(child: Text('No media available'));
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Media Gallery',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).primaryColor.withOpacity(0.8),
                  ),
                ),
                if (controller.selectedMedia.isNotEmpty)
                  FilledButton.icon(
                    icon: const Icon(IconlyBold.delete, size: 18),
                    label: const Text('Delete Selected'),
                    onPressed: () => _deleteSelectedMedia(controller),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.red.shade50,
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: controller.mediaItems.length,
              itemBuilder: (context, index) {
                final media = controller.mediaItems[index];
                final bool isSelected = controller.selectedMedia.contains(
                  media,
                );

                return GestureDetector(
                  onTap: () {
                    if (widget.multipleSelect) {
                      controller.toggleMediaSelection(media, true);
                    } else {
                      controller.selectSingleMedia(media);
                      Navigator.pop(context);

                      _navigateToConfirmationScreen(context, [media]);
                    }
                  },
                  onLongPress:
                      widget.multipleSelect
                          ? () {
                            showDialog(
                              context: context,
                              builder:
                                  (context) => Dialog(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              const BorderRadius.vertical(
                                                top: Radius.circular(12),
                                              ),
                                          child: Image.network(
                                            media.mediaUrl?.replaceAll(
                                                  ',',
                                                  '',
                                                ) ??
                                                '',
                                            fit: BoxFit.cover,
                                            height: 300,
                                            width: double.infinity,
                                            errorBuilder:
                                                (context, error, stackTrace) =>
                                                    const SizedBox(
                                                      height: 300,
                                                      child: Center(
                                                        child: Icon(
                                                          Icons.broken_image,
                                                          size: 64,
                                                          color: Colors.grey,
                                                        ),
                                                      ),
                                                    ),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(16.0),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                media.title ?? 'Untitled',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18,
                                                ),
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                'Type: ${media.type ?? 'Unknown'}',
                                              ),
                                              Text(
                                                'Size: ${_formatSize(media.sizeBytes ?? 0)}',
                                              ),
                                              const SizedBox(height: 16),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  TextButton(
                                                    onPressed:
                                                        () => Navigator.pop(
                                                          context,
                                                        ),
                                                    child: const Text('Close'),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                            );
                          }
                          : null,
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(7),
                          child: Image.network(
                            media.mediaUrl?.replaceAll(',', '') ?? '',
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return const Center(
                                child: Icon(Icons.broken_image, size: 40),
                              );
                            },
                          ),
                        ),
                      ),

                      if (isSelected)
                        Positioned(
                          top: 5,
                          right: 5,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),

                      Positioned(
                        top: 5,
                        right: isSelected ? 30 : 5,
                        child: GestureDetector(
                          onTap: () {
                            if (isSelected) {
                              controller.toggleMediaSelection(media, false);
                            }

                            controller.deleteMedia("${media.id}");
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              IconlyBold.delete,
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildUploadOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return FilledButton.icon(
      onPressed: onTap,
      style: FilledButton.styleFrom(
        minimumSize: const Size(double.infinity, 50),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
      icon: Icon(icon),
      label: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
    );
  }

  Widget _buildDragDropArea(BuildContext context, MediaController controller) {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (kIsWeb)
          DragTarget<List<dynamic>>(
            onAcceptWithDetails: (details) async {
              _isDragging = false;
              setState(() {});

              if (details.data.isNotEmpty) {
                try {
                  // Set the category in the controller before uploading
                  controller.selectedCategory.value = widget.category;

                  final files = details.data;
                  for (final file in files) {
                    if (file is XFile) {
                      await controller.uploadSingleMedia(file);
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error processing dropped files: $e'),
                      ),
                    );
                  }
                }
              }
            },
            onWillAccept: (data) {
              _isDragging = true;
              setState(() {});
              return true;
            },
            onLeave: (data) {
              _isDragging = false;
              setState(() {});
            },
            builder: (context, candidateData, rejectedData) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  color:
                      _isDragging
                          ? Theme.of(context).primaryColor.withOpacity(0.1)
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        _isDragging
                            ? Theme.of(context).primaryColor
                            : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      IconlyBold.upload,
                      size: 40,
                      color:
                          _isDragging
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isDragging
                          ? 'Drop files here'
                          : 'Drag and drop files here',
                      style: TextStyle(
                        color:
                            _isDragging
                                ? Theme.of(context).primaryColor
                                : Theme.of(
                                  context,
                                ).primaryColor.withOpacity(0.5),
                        fontWeight:
                            _isDragging ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            },
          )
        else
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyBold.upload,
                  size: 40,
                  color: Theme.of(context).primaryColor.withOpacity(0.5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Upload files from your device',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap:
                () => _pickImage(
                  ImageSource.gallery,
                  controller,
                  widget.category,
                ),
            child: SizedBox(width: double.infinity, height: double.infinity),
          ),
        ),
      ],
    );
  }

  Future<void> _pickImage(
    ImageSource source,
    MediaController controller,
    String category,
  ) async {
    if (controller.isUploading.value) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please wait for the current upload to complete'),
          ),
        );
      }
      return;
    }

    // Set the category in the controller
    controller.selectedCategory.value = category;

    try {
      if (widget.multipleSelect && source == ImageSource.gallery) {
        final List<XFile> images = await _picker.pickMultiImage();
        if (images.isNotEmpty) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder:
                (context) => const AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Uploading media...'),
                    ],
                  ),
                ),
          );

          await controller.uploadMultipleMedia(images);

          if (mounted && Navigator.canPop(context)) {
            Navigator.pop(context);
          }
        }
      } else {
        final XFile? image = await _picker.pickImage(source: source);
        if (image != null) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder:
                (context) => const AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Uploading media...'),
                    ],
                  ),
                ),
          );

          await controller.uploadSingleMedia(image);

          if (mounted && Navigator.canPop(context)) {
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error picking image: $e')));
      }
    }
  }

  Future<void> _deleteSelectedMedia(MediaController controller) async {
    final bool success = await controller.deleteSelectedMedia();
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Media deleted successfully')),
        );
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Failed to delete media')));
      }
    }
  }

  String _formatSize(int sizeBytes) {
    if (sizeBytes <= 0) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB'];
    double size = sizeBytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  void _navigateToConfirmationScreen(
    BuildContext context,
    List<MediaModel> selectedMedia,
  ) {
    // Ensure the category is set in the controller before navigating
    final MediaController controller = Get.find<MediaController>();
    controller.selectedCategory.value = widget.category;

    widget.onMediaSelected(selectedMedia);
  }
}
