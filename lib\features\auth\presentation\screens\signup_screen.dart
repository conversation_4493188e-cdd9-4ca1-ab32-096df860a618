import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/translations/app_translations.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import '../../controllers/auth_controller.dart';
import 'registration_steps.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final AuthController _authController = Get.find<AuthController>();
  int _currentStep = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    // Clear all registration-related text controllers
    _authController.firstNameController.clear();
    _authController.secondNameController.clear();
    _authController.emailController.clear();
    _authController.phoneController.clear();
    _authController.idNumberController.clear();
    _authController.referredByCodeController.clear();
    _authController.countyController.clear();
    _authController.subCountyController.clear();
    _authController.countryCodeController.clear();
    super.dispose();
  }

  void _nextStep() {
    if (_formKey.currentState!.validate()) {
      _animationController.reverse().then((_) {
        setState(() {
          _currentStep++;
        });
        _animationController.forward();
      });
    }
  }

  void _previousStep() {
    _animationController.reverse().then((_) {
      setState(() {
        _currentStep--;
      });
      _animationController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    // final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Get.offAllNamed(Routes.LOGIN),
          icon: Icon(IconlyLight.arrowLeft, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('create_account'),
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'step_x_of_y'.trParams({
                    'step': '${_currentStep + 1}',
                    'total': '4',
                  }),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 24),
                // Modern progress indicator
                Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: List.generate(4, (index) {
                      return Expanded(
                        child: Container(
                          margin: EdgeInsets.only(
                            left: index == 0 ? 0 : 2,
                            right: index == 3 ? 0 : 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                index <= _currentStep
                                    ? colorScheme.primary
                                    : colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                const SizedBox(height: 32),
                // Step content with fade animation
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildStepContent(),
                ),
                const SizedBox(height: 32),
                // Modern navigation
                Row(
                  children: [
                    if (_currentStep > 0)
                      Expanded(
                        child: _buildNavigationButton(
                          context,
                          icon: IconlyLight.arrowLeft,
                          label: context.tr('previous'),
                          onPressed: _previousStep,
                          isPrimary: false,
                        ),
                      ),
                    if (_currentStep > 0) const SizedBox(width: 16),
                    Expanded(
                      child: Obx(
                        () => _buildNavigationButton(
                          context,
                          icon:
                              _currentStep == 3
                                  ? IconlyLight.tickSquare
                                  : IconlyLight.arrowRight,
                          label: _currentStep == 3 ? 'sign_up'.tr : 'next'.tr,
                          onPressed:
                              _currentStep == 3
                                  ? () {
                                    if (_formKey.currentState!.validate()) {
                                      _authController.register(context);
                                    }
                                  }
                                  : _nextStep,
                          isLoading: _authController.isLoading.value,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Already have an account?',
                      style: TextStyle(
                        fontFamily: 'Alata',
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    TextButton(
                      onPressed: () => Get.offAllNamed(Routes.LOGIN),
                      child: Text(
                        'login'.tr,
                        style: TextStyle(
                          fontFamily: 'Alata',
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      persistentFooterButtons: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'already_have_account'.tr,
              style: TextStyle(
                fontFamily: 'Alata',
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            TextButton(
              onPressed: () => Get.offAllNamed(Routes.LOGIN),
              child: Text(
                'Login'.tr,
                style: TextStyle(
                  fontFamily: 'Alata',
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isPrimary = true,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(16),
        child: Ink(
          decoration: BoxDecoration(
            color:
                isPrimary
                    ? colorScheme.primary
                    : colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color:
                    isPrimary
                        ? colorScheme.primary.withValues(alpha: 0.3)
                        : colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isPrimary
                            ? colorScheme.onPrimary
                            : colorScheme.onSurfaceVariant,
                      ),
                    ),
                  )
                else
                  Icon(
                    icon,
                    color:
                        isPrimary
                            ? colorScheme.onPrimary
                            : colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color:
                        isPrimary
                            ? colorScheme.onPrimary
                            : colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return PersonalInfoStep(controller: _authController);
      case 1:
        return ContactInfoStep(controller: _authController);
      case 2:
        return LocationStep(controller: _authController);
      case 3:
        return AccountSetupStep(controller: _authController);
      default:
        return const SizedBox.shrink();
    }
  }
}
