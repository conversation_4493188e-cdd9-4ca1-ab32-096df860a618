import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../controllers/sms_requests_controller.dart';

class SmsRequestsFilterWidget extends StatelessWidget {
  const SmsRequestsFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsRequestsController>();
    final screenWidth = MediaQuery.of(context).size.width;

    Widget filterContent = Obx(
      () => Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Filter SMS Requests',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Start Date & Time',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap:
                        controller.isLoading.value
                            ? null
                            : () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate:
                                    controller.startDate.value ??
                                    DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime(2030),
                              );
                              if (date != null) {
                                final time = await showTimePicker(
                                  context: context,
                                  initialTime: TimeOfDay.fromDateTime(
                                    controller.startDate.value ??
                                        DateTime.now(),
                                  ),
                                );
                                if (time != null) {
                                  controller.startDate.value = DateTime(
                                    date.year,
                                    date.month,
                                    date.day,
                                    time.hour,
                                    time.minute,
                                  );
                                }
                              }
                            },
                    child: Opacity(
                      opacity: controller.isLoading.value ? 0.5 : 1.0,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              controller.startDate.value != null
                                  ? DateFormat(
                                    'dd MMM yyyy HH:mm',
                                  ).format(controller.startDate.value!)
                                  : 'Select Start Date & Time',
                            ),
                            const Icon(Icons.calendar_today),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'End Date & Time',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap:
                        controller.isLoading.value
                            ? null
                            : () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate:
                                    controller.endDate.value ?? DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime(2030),
                              );
                              if (date != null) {
                                final time = await showTimePicker(
                                  context: context,
                                  initialTime: TimeOfDay.fromDateTime(
                                    controller.endDate.value ?? DateTime.now(),
                                  ),
                                );
                                if (time != null) {
                                  controller.endDate.value = DateTime(
                                    date.year,
                                    date.month,
                                    date.day,
                                    time.hour,
                                    time.minute,
                                  );
                                }
                              }
                            },
                    child: Opacity(
                      opacity: controller.isLoading.value ? 0.5 : 1.0,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              controller.endDate.value != null
                                  ? DateFormat(
                                    'dd MMM yyyy HH:mm',
                                  ).format(controller.endDate.value!)
                                  : 'Select End Date & Time',
                            ),
                            const Icon(Icons.calendar_today),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: controller.phoneNumberController,
                    enabled: !controller.isLoading.value,
                    labelText: 'Phone Number',
                    hintText: 'Enter phone number to filter...',
                    prefixIcon: const Icon(Icons.phone),

                    onChanged:
                        (value) => controller.phoneNumberFilter.value = value,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed:
                            controller.isLoading.value
                                ? null
                                : () {
                                  controller.clearFilters();
                                  Navigator.pop(context);
                                },
                        child: const Text('Clear'),
                      ),
                      const SizedBox(width: 16),
                      FilledButton(
                        onPressed:
                            controller.isLoading.value
                                ? null
                                : () {
                                  controller.fetchMessages();
                                  Navigator.pop(context);
                                },
                        child: const Text('Apply'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          if (controller.isLoading.value)
            Positioned.fill(
              child: Container(
                color: Colors.black26,
                child: const Center(child: CircularProgressIndicator()),
              ),
            ),
        ],
      ),
    );

    // For larger screens, show in dialog
    if (screenWidth > 500) {
      return Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: filterContent,
        ),
      );
    }

    // For smaller screens, return the content directly for bottom sheet
    return filterContent;
  }
}
