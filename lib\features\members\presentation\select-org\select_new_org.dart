import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/services/http_service.dart' as ht;
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/cached_image.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/members/services/member_service2.dart';
import 'package:onechurch/features/members/models/new_organizations.dart';
import 'package:onechurch/features/members/presentation/select-org/form_fill_page.dart';
import 'package:onechurch/features/members/presentation/select-org/member_category_selection_dialog.dart';
import 'package:logger/logger.dart';

class SelectNewOrg extends StatefulWidget {
  final Function(NewOrganizations)? onOrganizationSelected;

  const SelectNewOrg({super.key, this.onOrganizationSelected});

  @override
  State<SelectNewOrg> createState() => _SelectNewOrgState();

  /// Shows the organization selection as a dialog or bottom sheet based on screen width
  static Future<NewOrganizations?> show(BuildContext context) {
    final bool isSmallScreen = MediaQuery.of(context).size.width < 500;

    if (isSmallScreen) {
      return showModalBottomSheet<NewOrganizations>(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => DraggableScrollableSheet(
              expand: false,
              initialChildSize: 0.7,
              maxChildSize: 0.9,
              minChildSize: 0.5,
              builder:
                  (context, scrollController) => Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Center(
                          child: Text(
                            'Select Organization',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: SelectNewOrg(
                            onOrganizationSelected: (org) {
                              Navigator.of(context).pop(org);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
            ),
      );
    } else {
      return showDialog<NewOrganizations>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Select Organization'),
              content: SizedBox(
                width: 500,
                height: 500,
                child: SelectNewOrg(
                  onOrganizationSelected: (org) {
                    Navigator.of(context).pop(org);
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
      );
    }
  }
}

class _SelectNewOrgState extends State<SelectNewOrg> {
  final TextEditingController _searchController = TextEditingController();
  final RxInt _currentPage = 0.obs;
  final RxInt _totalPages = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxList<NewOrganizations> _organizations = <NewOrganizations>[].obs;
  final RxString _searchQuery = ''.obs;
  final MemberService _memberService = MemberService();
  final logger = Get.find<Logger>();

  @override
  void initState() {
    super.initState();
    _loadOrganizations();

    _searchController.addListener(() {
      _searchQuery.value = _searchController.text;
      _currentPage.value = 0; // Reset to first page on new search
      _loadOrganizations();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadOrganizations() async {
    _isLoading.value = true;
    try {
      final result = await fetchOrganizations(
        page: _currentPage.value,
        search: _searchQuery.value,
      );
      _organizations.value = result.organizations;
      _totalPages.value = result.totalPages;
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'Failed to load organizations: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  void _nextPage() {
    if (_currentPage.value < _totalPages.value - 1) {
      _currentPage.value++;
      _loadOrganizations();
    }
  }

  void _previousPage() {
    if (_currentPage.value > 0) {
      _currentPage.value--;
      _loadOrganizations();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        CustomTextField(
          controller: _searchController,
          hintText: 'Search organizations',
          prefixIcon: const Icon(Icons.search),
        ),
        const SizedBox(height: 16),

        // Organizations list
        Expanded(
          child: Obx(() {
            if (_isLoading.value && _organizations.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_organizations.isEmpty) {
              return const Center(child: Text('No organizations found'));
            }

            return ListView.builder(
              itemCount: _organizations.length,
              itemBuilder: (context, index) {
                final org = _organizations[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(org.organisationName ?? 'Unknown'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(org.slogan ?? ''),
                        if (org.memberCategories != null &&
                            org.memberCategories!.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Wrap(
                              spacing: 4,
                              children:
                                  org.memberCategories!
                                      .map(
                                        (category) => Chip(
                                          label: Text(category.toString()),
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                          labelStyle: const TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                          ),
                        if (org.organisationLocations != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Row(
                              children: [
                                const Icon(Icons.location_on, size: 14),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    org.organisationLocations.toString(),
                                    style: const TextStyle(fontSize: 12),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                    leading: CircleAvatar(
                      child:
                          org.logo != null && org.logo!.isNotEmpty
                              ? CachedImage(imageUrl: org.logo!)
                              : const Icon(Icons.church),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () async {
                      // Show member category selection dialog
                      final selectedCategory =
                          await MemberCategorySelectionDialog.show(context);
                      final formData = await FormFill.showFormSheet(context);

                      if (selectedCategory != null) {
                        // Show loading indicator
                        Get.dialog(
                          const Center(child: CircularProgressIndicator()),
                          barrierDismissible: false,
                        );
                        try {
                          // Register member with the selected organization and category
                          final result = await _memberService.registerMember(
                            organisationId: org.organisationId ?? '',
                            memberCategoryId: selectedCategory.id ?? '',
                            categoryCode: selectedCategory.code ?? '',
                            formData: formData!,
                          );

                          // Close loading dialog
                          Get.back();

                          if (result['status'] == true) {
                            ToastUtils.showInfoToast(
                              'Success',
                              'Successfully joined organization',
                            );

                            // Call the callback if provided
                            if (widget.onOrganizationSelected != null) {
                              widget.onOrganizationSelected!(org);
                            }

                            // Close the dialog
                            Navigator.of(context).pop(org);
                          } else {
                            Navigator.of(context).pop();
                            ToastUtils.showInfoToast(
                              'Error',
                              result['message'] ??
                                  'Failed to join organization',

                            );
                          }
                        } catch (e) {
                          // Close loading dialog
                          Get.back();
                          ToastUtils.showInfoToast(
                            'Error',
                            'Failed to join organization: $e',

                          );
                          logger.e('Error joining organization: $e');
                        }
                      }
                    },
                  ),
                );
              },
            );
          }),
        ),

        // Pagination controls
        Obx(
          () => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _currentPage.value > 0 ? _previousPage : null,
                ),
                Text(
                  '${_currentPage.value + 1} / ${_totalPages.value > 0 ? _totalPages.value : 1}',
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed:
                      _currentPage.value < _totalPages.value - 1
                          ? _nextPage
                          : null,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Fetch organizations with pagination and search
  Future<OrganizationResult> fetchOrganizations({
    int page = 0,
    int size = 10,
    String? search,
  }) async {
    final ht.HttpService _httpService = Get.find();
    final Map<String, dynamic> params = {'page': page, 'size': size};

    // Add search parameter if provided
    if (search != null && search.isNotEmpty) {
      params['search'] = search;
    }

    final response = await _httpService.request(
      method: ht.Method.GET,
      url: 'organisation/general-organisations/',
      params: params,
    );

    if (response.statusCode == 200) {
      final data = response.data;
      final items = data['data']['items'] as List;
      final totalPages = data['data']['total_pages'] as int;

      return OrganizationResult(
        organizations:
            items.map((item) => NewOrganizations.fromJson(item)).toList(),
        totalPages: totalPages,
      );
    } else {
      throw Exception(
        'Failed to load organizations: ${response.statusMessage}',
      );
    }
  }
}
