import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../controllers/staff_roles_controller.dart';
import '../widgets/staff_role_filter_widget.dart';

class StaffRolesScreen extends StatefulWidget {
  const StaffRolesScreen({super.key});

  @override
  State<StaffRolesScreen> createState() => _StaffRolesScreenState();
}

class _StaffRolesScreenState extends State<StaffRolesScreen> {
  final StaffRolesController controller = Get.find<StaffRolesController>();
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    controller.fetchRoles();
  }

  List<PlutoColumn> columns() {
    return [
      PlutoColumn(
        title: 'Name',
        field: 'name',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Permissions',
        field: 'permissions_count',
        type: PlutoColumnType.number(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.date(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 200,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: Colors.blue),
                onPressed: () {
                  final roleId = rendererContext.cell.value as String;
                  final role = controller.roles.firstWhere(
                    (role) => role.id == roleId,
                  );
                  context.go(Routes.CREATE_STAFF_ROLE, extra: role);
                },
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  final roleId = rendererContext.cell.value as String;
                  _showDeleteConfirmationDialog(roleId);
                },
              ),
            ],
          );
        },
      ),
    ];
  }

  List<PlutoRow> rows() {
    return controller.roles.map((role) {
      return PlutoRow(
        cells: {
          'name': PlutoCell(value: role.name ?? 'No Name'),
          'description': PlutoCell(value: role.description ?? 'No Description'),
          'status': PlutoCell(value: role.status ?? 'Unknown'),
          'permissions_count': PlutoCell(value: role.permissions?.length ?? 0),
          'created_at': PlutoCell(value: role.createdAt ?? DateTime.now()),
          'actions': PlutoCell(value: role.id ?? ''),
        },
      );
    }).toList();
  }

  void _showDeleteConfirmationDialog(String roleId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content: const Text('Are you sure you want to delete this role?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                controller.deleteRole(roleId);
                Navigator.of(context).pop();
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Staff Roles'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.go(Routes.CREATE_STAFF_ROLE);
            },
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  child: Column(
                    children: [
                      const StaffRoleFilterWidget(),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Staff Roles',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  height:
                                      MediaQuery.of(context).size.height * 0.6,
                                  child: PlutoGrid(
                                    columns: columns(),
                                    rows: rows(),
                                    onLoaded: (PlutoGridOnLoadedEvent event) {
                                      stateManager = event.stateManager;
                                    },
                                    configuration: PlutoGridConfiguration(
                                      columnFilter: PlutoGridColumnFilterConfig(
                                        filters: [
                                          ...FilterHelper.defaultFilters,
                                        ],
                                        resolveDefaultColumnFilter: (
                                          column,
                                          resolver,
                                        ) {
                                          if (column.field == 'name') {
                                            return resolver<
                                                  PlutoFilterTypeContains
                                                >()
                                                as PlutoFilterType;
                                          } else if (column.field == 'status') {
                                            return resolver<
                                                  PlutoFilterTypeContains
                                                >()
                                                as PlutoFilterType;
                                          }
                                          return resolver<
                                                PlutoFilterTypeContains
                                              >()
                                              as PlutoFilterType;
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }
}
