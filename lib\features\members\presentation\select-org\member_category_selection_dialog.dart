import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/members/models/member_category_model.dart';
import 'package:onechurch/features/members/services/member_service2.dart';

class MemberCategorySelectionDialog extends StatefulWidget {
  final Function(MemberCategory) onCategorySelected;

  const MemberCategorySelectionDialog({
    super.key,
    required this.onCategorySelected,
  });

  @override
  State<MemberCategorySelectionDialog> createState() =>
      _MemberCategorySelectionDialogState();

  /// Shows the member category selection as a dialog or bottom sheet based on screen width
  static Future<MemberCategory?> show(BuildContext context) {
    final bool isSmallScreen = MediaQuery.of(context).size.width < 500;

    if (isSmallScreen) {
      return showModalBottomSheet<MemberCategory>(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => DraggableScrollableSheet(
              expand: false,
              initialChildSize: 0.7,
              maxChildSize: 0.9,
              minChildSize: 0.5,
              builder:
                  (context, scrollController) => Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Center(
                          child: Text(
                            'Select Member Category',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: MemberCategorySelectionDialog(
                            onCategorySelected: (category) {
                              Navigator.of(context).pop(category);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
            ),
      );
    } else {
      return showDialog<MemberCategory>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Select Member Category'),
              content: SizedBox(
                width: 500,
                height: 500,
                child: MemberCategorySelectionDialog(
                  onCategorySelected: (category) {
                    Navigator.of(context).pop(category);
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
      );
    }
  }
}

class _MemberCategorySelectionDialogState
    extends State<MemberCategorySelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  final RxInt _currentPage = 0.obs;
  final RxInt _totalPages = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxList<MemberCategory> _categories = <MemberCategory>[].obs;
  final RxString _searchQuery = ''.obs;
  final MemberService _memberService = MemberService();

  @override
  void initState() {
    super.initState();
    _loadCategories();

    _searchController.addListener(() {
      _searchQuery.value = _searchController.text;
      _currentPage.value = 0;
      _loadCategories();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    _isLoading.value = true;
    try {
      final result = await _memberService.fetchMemberCategories(
        page: _currentPage.value,
        search: _searchQuery.value,
      );

      if (result.status == true && result.data != null && result.data != null) {
        _categories.value = result.data!;
        _totalPages.value = result.data?.length ?? 0;
      } else {
        ToastUtils.showErrorToast(
          'Error',
          result.message ?? 'Failed to load categories',
        );
        _categories.clear();
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'Failed to load categories: $e');
      _categories.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  void _nextPage() {
    if (_currentPage.value < _totalPages.value - 1) {
      _currentPage.value++;
      _loadCategories();
    }
  }

  void _previousPage() {
    if (_currentPage.value > 0) {
      _currentPage.value--;
      _loadCategories();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        CustomTextField(
          controller: _searchController,
          hintText: 'Search categories',
          prefixIcon: const Icon(Icons.search),
        ),
        const SizedBox(height: 16),

        // Categories list
        Expanded(
          child: Obx(() {
            if (_isLoading.value && _categories.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_categories.isEmpty) {
              return const Center(child: Text('No categories found'));
            }

            return ListView.builder(
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(category.title ?? 'Unknown'),
                    subtitle: Text(category.description ?? ''),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      widget.onCategorySelected(category);
                    },
                  ),
                );
              },
            );
          }),
        ),

        // Pagination controls
        Obx(
          () => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _currentPage.value > 0 ? _previousPage : null,
                ),
                Text(
                  '${_currentPage.value + 1} / ${_totalPages.value > 0 ? _totalPages.value : 1}',
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed:
                      _currentPage.value < _totalPages.value - 1
                          ? _nextPage
                          : null,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
