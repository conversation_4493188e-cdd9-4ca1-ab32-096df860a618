import 'dart:io';
import 'package:excel/excel.dart' as ex;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
// Conditional import for web platform
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html if (dart.library.html) 'dart:html';

import '../../../../core/app/utils/show_toast.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/member_controller.dart';

class MemberImportHelperScreen extends StatefulWidget {
  const MemberImportHelperScreen({super.key});

  @override
  State<MemberImportHelperScreen> createState() =>
      _MemberImportHelperScreenState();
}

class _MemberImportHelperScreenState extends State<MemberImportHelperScreen> {
  final controller = Get.find<MemberController>();
  final logger = Get.find<Logger>();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Member Import Helper'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(Routes.ADD_MEMBER),
            tooltip: 'Add Member Manually',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Import Members from Excel',
                style: theme.textTheme.headlineSmall,
              ),
              Gap(8.h),
              Text(
                'Use this tool to bulk import members from an Excel file.',
                style: theme.textTheme.bodyLarge,
              ),
              Gap(24.h),

              // Download Template Section
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(IconlyBold.document, color: theme.primaryColor),
                          Gap(8.w),
                          Text(
                            'Download Template',
                            style: theme.textTheme.titleLarge,
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Text(
                        'Download our Excel template with the correct format for importing members. The template includes all required fields and an example entry.',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Gap(16.h),
                      SizedBox(
                        width: double.infinity,
                        child: FilledButton.icon(
                          icon: const Icon(Icons.download),
                          label: const Text('Download Excel Template'),
                          onPressed: () => _downloadExcelTemplate(context),
                        ),
                      ),
                      Gap(16.h),
                      Text(
                        'Note: You can also create your own Excel file following the instructions below.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(24.h),

              // Import Instructions
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            IconlyBold.infoSquare,
                            color: theme.primaryColor,
                          ),
                          Gap(8.w),
                          Text(
                            'Import Instructions',
                            style: theme.textTheme.titleLarge,
                          ),
                        ],
                      ),
                      Gap(16.h),

                      _buildInstructionStep(
                        '1',
                        'Prepare Your Excel File',
                        'Create a new Excel file or use our template. Ensure your Excel file has the correct column headers in the first row as specified below.',
                        theme,
                      ),

                      _buildInstructionStep(
                        '2',
                        'Fill in Member Data',
                        'Add member information in rows below the header row. Each row represents one member. Follow the format guidelines for each field.',
                        theme,
                      ),

                      _buildInstructionStep(
                        '3',
                        'Format Special Fields',
                        'Ensure dates are in YYYY-MM-DD format. For fields with specific values (like Gender, Marital Status), use the exact values specified below.',
                        theme,
                      ),

                      _buildInstructionStep(
                        '4',
                        'Save as Excel File',
                        'Save your file as an Excel file (.xlsx format). Make sure to save it in a location you can easily access.',
                        theme,
                      ),

                      _buildInstructionStep(
                        '5',
                        'Import the File',
                        'Go to the Add Members screen and click the "Upload Excel" button to import your file. Review the data before final submission.',
                        theme,
                      ),

                      Gap(24.h),
                      Text(
                        'Excel Column Headers',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(12.h),
                      Text(
                        'Your Excel file must have the following column headers in the first row:',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Gap(12.h),

                      // Required Fields
                      Text(
                        'Required Fields:',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      Gap(8.h),
                      _buildFieldDescription(
                        'First Name',
                        'Required - Member\'s first name',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Second Name',
                        'Required - Member\'s last name',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Phone Number',
                        'Required - Format: +************',
                        theme,
                      ),

                      Gap(16.h),
                      // Important Fields
                      Text(
                        'Important Fields:',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.secondary,
                        ),
                      ),
                      Gap(8.h),
                      _buildFieldDescription(
                        'Email',
                        'Recommended - Valid email format',
                        theme,
                      ),
                      _buildFieldDescription(
                        'ID Number',
                        'Recommended - National ID number',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Join Date',
                        'Format: YYYY-MM-DD (e.g., 2023-01-15)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Date of Birth',
                        'Format: YYYY-MM-DD (e.g., 1985-05-20)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Gender',
                        'Values: MALE, FEMALE, OTHER',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Marital Status',
                        'Values: SINGLE, MARRIED, DIVORCED, WIDOWED',
                        theme,
                      ),

                      Gap(16.h),
                      // Additional Fields
                      Text(
                        'Additional Fields:',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(8.h),
                      _buildFieldDescription(
                        'Address',
                        'Member\'s physical address',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Occupation',
                        'Member\'s job or profession',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Education Level',
                        'Values: NONE, PRIMARY, SECONDARY, DIPLOMA, DEGREE, MASTERS, PHD',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Nationality',
                        'Member\'s country of citizenship',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Tribe',
                        'Member\'s ethnic group (if applicable)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Disability',
                        'Values: NONE, PHYSICAL, VISUAL, HEARING, COGNITIVE, OTHER',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Employment Status',
                        'Values: EMPLOYED, SELF_EMPLOYED, UNEMPLOYED, STUDENT, RETIRED',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Income Bracket',
                        'E.g., <10k, 10k-50k, 50k-100k, >100k',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Household Size',
                        'Number of people in household (numeric)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Housing Type',
                        'Values: PERMANENT, SEMI_PERMANENT, TEMPORARY, RENTAL',
                        theme,
                      ),

                      Gap(24.h),
                      Text(
                        'Tips for Successful Import',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(12.h),
                      Padding(
                        padding: EdgeInsets.only(left: 8.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildTip(
                              'Ensure all required fields are filled for each member.',
                              theme,
                            ),
                            _buildTip(
                              'Use the exact values for fields with specific options (like Gender, Marital Status).',
                              theme,
                            ),
                            _buildTip(
                              'Format dates as YYYY-MM-DD to avoid import errors.',
                              theme,
                            ),
                            _buildTip(
                              'Check for duplicate phone numbers before importing.',
                              theme,
                            ),
                            _buildTip(
                              'Limit your import to 100 members at a time for best performance.',
                              theme,
                            ),
                            _buildTip(
                              'Review the data in the preview before final submission.',
                              theme,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(24.h),

              // Field Descriptions
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(IconlyBold.paper, color: theme.primaryColor),
                          Gap(8.w),
                          Text(
                            'Required Fields',
                            style: theme.textTheme.titleLarge,
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Text(
                        'The following fields are included in the template:',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Gap(16.h),
                      _buildFieldDescription('First Name', 'Required', theme),
                      _buildFieldDescription('Second Name', 'Required', theme),
                      _buildFieldDescription(
                        'Phone Number',
                        'Required (format: +************)',
                        theme,
                      ),
                      _buildFieldDescription('Email', 'Optional', theme),
                      _buildFieldDescription('ID Number', 'Optional', theme),
                      _buildFieldDescription('Address', 'Optional', theme),
                      _buildFieldDescription(
                        'Join Date',
                        'Optional (format: YYYY-MM-DD)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Date of Birth',
                        'Optional (format: YYYY-MM-DD)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Gender',
                        'Optional (Male/Female/Other)',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Marital Status',
                        'Optional',
                        theme,
                      ),
                      _buildFieldDescription('Occupation', 'Optional', theme),
                      _buildFieldDescription(
                        'Education Level',
                        'Optional',
                        theme,
                      ),
                      _buildFieldDescription('Nationality', 'Optional', theme),
                      _buildFieldDescription(
                        'Internal Account',
                        'Optional',
                        theme,
                      ),
                      _buildFieldDescription(
                        'Member Account',
                        'Optional',
                        theme,
                      ),
                    ],
                  ),
                ),
              ),
              Gap(24.h),

              // Go to Add Member Button
              SizedBox(
                width: double.infinity,
                child: FilledButton.icon(
                  icon: const Icon(Icons.upload_file),
                  label: const Text('Go to Add Member Screen'),
                  onPressed: () => context.go(Routes.ADD_MEMBER),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructionStep(
    String number,
    String title,
    String description,
    ThemeData theme,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24.r,
            height: 24.r,
            decoration: BoxDecoration(
              color: theme.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Gap(4.h),
                Text(description, style: theme.textTheme.bodyMedium),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFieldDescription(
    String fieldName,
    String requirement,
    ThemeData theme,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(IconlyLight.tickSquare, size: 16.r, color: theme.primaryColor),
          Gap(8.w),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: theme.textTheme.bodyMedium,
                children: [
                  TextSpan(
                    text: fieldName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: ' - $requirement'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTip(String tipText, ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info, size: 16.r, color: theme.colorScheme.secondary),
          Gap(8.w),
          Expanded(child: Text(tipText, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Future<void> _downloadExcelTemplate(BuildContext context) async {
    try {
      // Create Excel file
      final excel = ex.Excel.createExcel();
      final sheet = excel['Members'];

      // Define headers based on MemberModel fields
      final headers = [
        'First Name',
        'Second Name',
        'Phone Number',
        'Email',
        'ID Number',
        'Address',
        'Join Date',
        'Date of Birth',
        'Gender',
        'Marital Status',
        'Occupation',
        'Education Level',
        'Nationality',
        'Internal Account',
        'Member Account',
      ];

      // Add headers to first row
      for (var i = 0; i < headers.length; i++) {
        final cell = sheet.cell(
          ex.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
        );
        cell.value = ex.TextCellValue(headers[i]);
        // Style the header
        cell.cellStyle = ex.CellStyle(
          bold: true,
          horizontalAlign: ex.HorizontalAlign.Center,
        );
      }

      // Add example data in second row
      final exampleData = [
        'John',
        'Doe',
        '+************',
        '<EMAIL>',
        'ID123456',
        '123 Main St, Nairobi',
        '2023-01-15', // Join Date
        '1985-05-20', // Date of Birth
        'Male',
        'Married',
        'Engineer',
        'University',
        'Kenyan',
        'INT001',
        'MEM001',
      ];

      for (var i = 0; i < exampleData.length; i++) {
        final cell = sheet.cell(
          ex.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 1),
        );
        cell.value = ex.TextCellValue(exampleData[i]);
      }

      // Auto-size columns
      for (var i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 20);
      }

      // Convert to bytes
      final bytes = excel.encode();
      if (bytes == null) {
        ToastUtils.showErrorToast("Failed to generate Excel file", "Error");
        return;
      }

      // Save the file (implementation depends on platform)
      if (kIsWeb) {
        // For web platform
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.AnchorElement(href: url)
          ..setAttribute('download', 'members_import_template.xlsx')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        // For mobile platforms
        final directory = await getApplicationDocumentsDirectory();
        final path = '${directory.path}/members_import_template.xlsx';
        final file = File(path);
        await file.writeAsBytes(bytes);
        await Share.shareXFiles([XFile(path)], text: 'Members Import Template');
      }

      ToastUtils.showSuccessToast(
        "Template downloaded successfully",
        "Success",
      );
    } catch (e) {
      logger.e("Error downloading template: $e");
      ToastUtils.showErrorToast("Error downloading template: $e", "Error");
    }
  }
}
