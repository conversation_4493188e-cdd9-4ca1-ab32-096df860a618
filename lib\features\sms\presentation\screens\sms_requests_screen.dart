import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:onechurch/data/models/sms_model.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../core/app/utils/size_config.dart';
import '../../controllers/sms_requests_controller.dart';
import '../widgets/new_sms_requests_filter_widget.dart';

class SmsRequestsScreen extends StatefulWidget {
  const SmsRequestsScreen({super.key});

  @override
  State<SmsRequestsScreen> createState() => _SmsRequestsScreenState();
}

class _SmsRequestsScreenState extends State<SmsRequestsScreen> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final controller = Get.find<SmsRequestsController>();

  List<PlutoRow> setupRows(List<SmsModel> messages) {
    return messages.map((message) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: message.id ?? ''),
          'message': PlutoCell(value: message.message ?? ''),
          'status': PlutoCell(value: message.status ?? 'Pending'),
          'created_at': PlutoCell(
            value:
                message.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm a',
                    ).format(message.createdAt!)
                    : 'N/A',
          ),
          'delivered_at': PlutoCell(
            value:
                message.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm a',
                    ).format(message.createdAt!)
                    : 'N/A',
          ),
          'actions': PlutoCell(value: message.id),
        },
      );
    }).toList();
  }

  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
      ),
      PlutoColumn(
        title: 'Message',
        field: 'message',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.row.cells['status']?.value;
          return Chip(
            backgroundColor:
                status == "Delivered"
                    ? Colors.green.shade100.withOpacity(0.2)
                    : Colors.orange.shade100.withOpacity(0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: status == "Delivered" ? Colors.green : Colors.orange,
                width: 0.6,
              ),
            ),
            label: Text(
              status.toString(),
              style: TextStyle(
                color: status == "Delivered" ? Colors.green : Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Delivered At',
        field: 'delivered_at',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, size: 20),
                onPressed: () {
                  final messageId = rendererContext.row.cells['id']?.value;
                  if (messageId != null) {
                    // View message details
                    final message = controller.messages.firstWhere(
                      (msg) => msg.id == messageId,
                      orElse: () => SmsModel(),
                    );

                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: const Text('Request Details'),
                            content: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text('ID: ${message.id ?? "N/A"}'),
                                  const Divider(),
                                  Text('Content: ${message.message ?? "N/A"}'),
                                  const Divider(),
                                  Text(
                                    'Status: ${message.status ?? "Pending"}',
                                  ),
                                  const Divider(),
                                  Text(
                                    'Created: ${message.createdAt != null ? DateFormat('dd MMM yyyy HH:mm a').format(message.createdAt!) : "N/A"}',
                                  ),
                                  const Divider(),
                                  Text(
                                    'Delivered: ${message.createdAt != null ? DateFormat('dd MMM yyyy HH:mm a').format(message.createdAt!) : "N/A"}',
                                  ),
                                ],
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                    );
                  }
                },
              ),
            ],
          );
        },
      ),
    ];
  }

  @override
  void initState() {
    setColumns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SMS Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Messages',
            onPressed: () => controller.fetchMessages(),
          ),
          // Filter button removed as we now use the inline filter widget
        ],
      ),
      body: Obx(
        () => Stack(
          children: [
            SizedBox(
              height: SizeConfig.screenHeight,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const NewSmsRequestsFilterWidget(),

                    // Messages list with PlutoGrid
                    SizedBox(
                      height: SizeConfig.screenHeight * .6,
                      child: Card(
                        color: Theme.of(context).secondaryHeaderColor,
                        margin: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 4,
                        child: PlutoGrid(
                          mode: PlutoGridMode.selectWithOneTap,
                          columns: columns,
                          rows: rows,
                          onLoaded: (PlutoGridOnLoadedEvent event) {
                            event.stateManager.setShowColumnFilter(true);
                            if (kDebugMode) {
                              debugPrint("onLoaded: $event");
                            }
                          },
                          onSelected: (event) {
                            if (kDebugMode) {
                              debugPrint("onSelected: $event");
                            }
                          },
                          configuration: PlutoGridConfiguration(
                            style: PlutoGridStyleConfig(
                              activatedColor: const Color.fromARGB(
                                255,
                                165,
                                205,
                                253,
                              ),
                              cellTextStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(255, 216, 108, 40),
                              ),
                              columnTextStyle: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blueGrey,
                              ),
                            ),
                            columnSize: const PlutoGridColumnSizeConfig(
                              autoSizeMode: PlutoAutoSizeMode.scale,
                              resizeMode: PlutoResizeMode.normal,
                            ),
                          ),
                          createFooter: (stateManager) {
                            return PlutoLazyPagination(
                              initialPage: 0,
                              initialFetch: true,
                              fetchWithSorting: true,
                              fetchWithFiltering: true,
                              pageSizeToMove: null,
                              fetch: (pagReq) async {
                                controller.currentPage.value = pagReq.page;
                                debugPrint("fetch page: ${pagReq.page}");

                                await controller.fetchMessages();
                                if (controller.errorMessage.isNotEmpty) {
                                  ToastUtils.showErrorToast(
                                    controller.errorMessage.value,
                                    null,
                                  );
                                }

                                return Future.value(
                                  PlutoLazyPaginationResponse(
                                    totalPage: controller.totalPages.value,
                                    rows: setupRows(controller.messages),
                                  ),
                                );
                              },
                              stateManager: stateManager,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (controller.isLoading.value)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
      ),
    );
  }
}
