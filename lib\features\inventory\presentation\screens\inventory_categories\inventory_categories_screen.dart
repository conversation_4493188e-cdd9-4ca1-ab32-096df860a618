import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/loading_animations.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../controllers/inventory_category_controller.dart';

class InventoryCategoriesScreen extends StatefulWidget {
  const InventoryCategoriesScreen({super.key});

  @override
  State<InventoryCategoriesScreen> createState() =>
      _InventoryCategoriesScreenState();
}

class _InventoryCategoriesScreenState extends State<InventoryCategoriesScreen> {
  final controller = Get.find<InventoryCategoryController>();
  final searchController = TextEditingController();
  late PlutoGridStateManager stateManager;

  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];

  @override
  void initState() {
    super.initState();
    _initializeColumns();
    _setupListeners();
  }

  void _initializeColumns() {
    columns = [
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Code',
        field: 'code',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 250,
      ),
      PlutoColumn(
        title: 'General',
        field: 'isGeneral',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Active',
        field: 'isActive',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(IconlyLight.edit, size: 18),
                onPressed: () => _editCategory(rendererContext.row),
                tooltip: 'Edit',
              ),
              IconButton(
                icon: const Icon(IconlyLight.delete, size: 18),
                onPressed: () => _deleteCategory(rendererContext.row),
                tooltip: 'Delete',
              ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        hide: true,
      ),
    ];
  }

  void _setupListeners() {
    // Listen to categories changes and update grid
    ever(controller.categories, (_) => _updatePlutoRows());
  }

  void _updatePlutoRows() {
    // Clear existing rows
    rows.clear();

    // Add a row for each category
    for (int i = 0; i < controller.categories.length; i++) {
      final category = controller.categories[i];
      rows.add(
        PlutoRow(
          cells: {
            'title': PlutoCell(value: category.title),
            'code': PlutoCell(value: category.code),
            'description': PlutoCell(value: category.description ?? ''),
            'isGeneral': PlutoCell(
              value: category.isGeneral ?? false ? 'Yes' : 'No',
            ),
            'isActive': PlutoCell(
              value: category.isActive ?? false ? 'Yes' : 'No',
            ),
            'actions': PlutoCell(value: ''),
            'id': PlutoCell(value: category.id),
          },
        ),
      );
    }

    // Refresh grid if initialized
    if (mounted) {
      stateManager.notifyListeners();
    }
  }

  void _editCategory(PlutoRow row) {
    final categoryId = row.cells['id']?.value;
    if (categoryId != null) {
      context.go(Routes.EDIT_INVENTORY_CATEGORY.replaceAll(':id', categoryId));
    }
  }

  void _deleteCategory(PlutoRow row) {
    final categoryId = row.cells['id']?.value;
    final categoryTitle = row.cells['title']?.value;

    if (categoryId != null) {
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Delete Category'),
              content: Text(
                'Are you sure you want to delete "$categoryTitle"?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                FilledButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    controller.deleteCategory(categoryId);
                  },
                  child: const Text('Delete'),
                ),
              ],
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Inventory Categories'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Inventory Categories',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(8.h),
                      Text(
                        'Manage your inventory item categories',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                FilledButton.icon(
                  onPressed: () {
                    context.go(Routes.ADD_INVENTORY_CATEGORY);
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Category'),
                ),
              ],
            ),
            Gap(24.h),

            // Search and filters
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CustomTextField(
                    controller: searchController,
                    hintText: 'Search categories...',
                    prefixIcon: const Icon(IconlyLight.search),
                    onChanged: (value) {
                      controller.searchCategories(value);
                    },
                  ),
                ),
                Gap(16.w),
                IconButton(
                  onPressed: () {
                    searchController.clear();
                    controller.refreshCategories();
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh',
                ),
              ],
            ),
            Gap(24.h),

            // Categories list with Pluto Grid
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value &&
                    controller.categories.isEmpty) {
                  return const Center(child: LoadingAnimations());
                }

                if (controller.categories.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          IconlyLight.category,
                          size: 64,
                          color: theme.colorScheme.primary.withOpacity(0.5),
                        ),
                        Gap(16.h),
                        Text(
                          'No categories found',
                          style: theme.textTheme.titleMedium,
                        ),
                        Gap(8.h),
                        Text(
                          'Click the + button to add a category',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Gap(24.h),
                        FilledButton.icon(
                          onPressed: () {
                            context.go(Routes.ADD_INVENTORY_CATEGORY);
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Add Category'),
                        ),
                      ],
                    ),
                  );
                }

                return PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    _updatePlutoRows();
                  },
                  configuration: PlutoGridConfiguration(
                    style: PlutoGridStyleConfig(
                      gridBorderRadius: BorderRadius.circular(8),
                      enableRowColorAnimation: true,
                    ),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
