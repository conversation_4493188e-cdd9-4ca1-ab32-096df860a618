import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/core/widgets/custom_phone_input.dart';

import '../../../../core/app/constants/routes.dart';
import '../../controllers/attendance_controller.dart';

class MarkAttendanceScreen extends StatefulWidget {
  final String? attendanceId;

  const MarkAttendanceScreen({super.key, this.attendanceId});

  @override
  State<MarkAttendanceScreen> createState() => _MarkAttendanceScreenState();
}

class _MarkAttendanceScreenState extends State<MarkAttendanceScreen> {
  final AttendanceController _attendanceController =
      Get.find<AttendanceController>();
  final logger = Get.find<Logger>();
  final _formKey = GlobalKey<FormState>();
  

  bool get isEditing => widget.attendanceId != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {}
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Attendance' : 'Mark Attendance'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(Routes.ATTENDANCE),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Attendance Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Divider(),
                        Gap(16.h),

                        // Title field
                        CustomTextFormField(
                          controller: _attendanceController.titleController,
                          labelText: 'Event Title',
                          hintText: 'Enter event title',
                          prefixIcon: const Icon(IconlyLight.document),

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter event title';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                        MarkupEditorWidget(
                          label: "Description",
                          isRequired: true,
                          hintText: 'Enter event description',
                          height: 200.h,
                          initialValue:
                              _attendanceController.descriptionController.text,
                          onChanged: (content) {
                            _attendanceController.description.value = content;
                          },
                        ),
                        Gap(16.h),

                        // Attendee Name field
                        CustomTextFormField(
                          controller:
                              _attendanceController.attendeeNameFormController,
                          labelText: 'Attendee Name',
                          hintText: 'Enter attendee name',
                          prefixIcon: const Icon(IconlyLight.profile),

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter attendee name';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                        // Attendee Phone field
                        CustomPhoneInput(
                          label: 'Attendee Phone',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter attendee phone number';
                            }
                            return null;
                          },
                          controller: _attendanceController.attendeePhoneController,
                          onPhoneNumberChanged: (PhoneNumber number) {
                            _attendanceController.phoneNumber(number.phoneNumber);
                          },
                        ),
                        Gap(16.h),

                        // Attendee Email field
                        CustomTextFormField(
                          controller:
                              _attendanceController.attendeeEmailController,
                          labelText: 'Attendee Email',
                          hintText: 'Enter attendee email (optional)',
                          prefixIcon: const Icon(IconlyLight.message),

                          keyboardType: TextInputType.emailAddress,
                        ),
                        Gap(16.h),

                        // Location field
                        CustomTextFormField(
                          controller:
                              _attendanceController.locationNameController,
                          labelText: 'Location',
                          hintText: 'Enter location name',
                          prefixIcon: const Icon(IconlyLight.location),

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter location';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                         ],
                    ),
                  ),
                ),

                Gap(24.h),

                // Submit button
                Center(
                  child: Obx(
                    () =>
                        _attendanceController.isLoading.value
                            ? const CircularProgressIndicator()
                            : SizedBox(
                              width: 200.w,
                              height: 50.h,
                              child: FilledButton.icon(
                                onPressed: _submitForm,
                                icon: const Icon(IconlyBold.tickSquare),
                                label: Text(
                                  isEditing
                                      ? 'Update Attendance'
                                      : 'Mark Attendance',
                                ),
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      _attendanceController.markAttendance().then((success) {
        if (success) {
          context.go(Routes.ATTENDANCE);
        }
      });
    }
  }
}
