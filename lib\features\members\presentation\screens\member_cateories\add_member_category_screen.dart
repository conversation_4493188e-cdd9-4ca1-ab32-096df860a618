import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../../../core/app/utils/show_toast.dart';
import '../../../controllers/member_category_controller.dart';

class AddMemberCategoryScreen extends StatefulWidget {
  const AddMemberCategoryScreen({super.key});

  @override
  State<AddMemberCategoryScreen> createState() =>
      _AddMemberCategoryScreenState();
}

class _AddMemberCategoryScreenState extends State<AddMemberCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final controller = Get.find<MemberCategoryController>();

  // Form controllers
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final codeController = TextEditingController();

  // Form values
  bool isGeneral = false;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    codeController.dispose();
    super.dispose();
  }

  // Auto-generate code from title
  void _generateCodeFromTitle(String title) {
    if (title.isEmpty) {
      codeController.text = '';
      return;
    }

    // Convert title to uppercase and remove spaces
    final words = title.split(' ');
    if (words.length == 1) {
      // If single word, take first 6 characters or the whole word if shorter
      final word = words[0].toUpperCase();
      codeController.text = word.length > 6 ? word.substring(0, 6) : word;
    } else {
      // If multiple words, take first letter of each word up to 6 characters
      String code = '';
      for (final word in words) {
        if (word.isNotEmpty) {
          code += word[0].toUpperCase();
          if (code.length >= 6) break;
        }
      }
      codeController.text = code;
    }
  }

  // Submit form
  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      final result = await controller.createCategory(
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        code: codeController.text.trim().toUpperCase(),
        isGeneral: isGeneral,
      );

      if (result) {
        ToastUtils.showSuccessToast('Category created successfully', null);
        context.go(Routes.MEMBER_CATEGORIES);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Add Member Category')),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Info banner
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    Icon(
                      IconlyLight.infoSquare,
                      color: theme.colorScheme.primary,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Create Member Category',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            'Member categories help you organize and group members based on their roles or characteristics.',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gap(24.h),

              // Title
              CustomTextFormField(
                controller: titleController,
                labelText: 'Title',
                hintText: 'e.g., Youth Member, Elder, Deacon',
                prefixIcon: const Icon(IconlyLight.category),

                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
                onChanged: _generateCodeFromTitle,
              ),
              Gap(16.h),

              // Code
              CustomTextFormField(
                controller: codeController,
                labelText: 'Code',
                hintText: 'e.g., YOUTH, ELDER, DEACON',
                prefixIcon: const Icon(IconlyLight.document),

                helperText:
                    'A unique identifier for this category (max 6 characters)',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a code';
                  }
                  if (value.length > 6) {
                    return 'Code must be 6 characters or less';
                  }
                  return null;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                  LengthLimitingTextInputFormatter(6),
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    return TextEditingValue(
                      text: newValue.text.toUpperCase(),
                      selection: newValue.selection,
                    );
                  }),
                ],
              ),
              Gap(16.h),

              // Description
              CustomTextFormField(
                controller: descriptionController,
                labelText: 'Description',
                hintText: 'Describe this category...',
                prefixIcon: const Icon(IconlyLight.paper),

                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              Gap(16.h),

              // Submit button
              SizedBox(
                width: double.infinity,
                child: Obx(
                  () => FilledButton(
                    onPressed:
                        controller.isSubmitting.value ? null : _submitForm,
                    child:
                        controller.isSubmitting.value
                            ? const CircularProgressIndicator.adaptive()
                            : const Text('Create Category'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
