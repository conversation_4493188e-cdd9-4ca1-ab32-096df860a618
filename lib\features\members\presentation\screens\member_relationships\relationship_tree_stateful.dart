import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Node;
import 'package:graphview/graphview.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import '../../../controllers/relationship_controller.dart';
import '../../../models/relationship_models.dart';
import 'relationship_tree_utils.dart';

/// Build a relationship tree visualization
Widget buildRelationshipTree(
  BuildContext context,
  RelationshipController relationshipController,
  String memberId,
) {
  return Obx(() {
    if (relationshipController.isLoadingRelationships.value) {
      return const Center(child: CircularProgressIndicator());
    }

    if (relationshipController.relationships.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              IconlyLight.user3,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No relationships found',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Add relationships to see them here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }

    // Use a StatefulWidget for the tree to prevent rebuilding issues
    return RelationshipTreeView(
      relationshipController: relationshipController,
      memberId: memberId,
    );
  });
}

/// A stateful widget for the relationship tree to prevent flashing
class RelationshipTreeView extends StatefulWidget {
  final RelationshipController relationshipController;
  final String memberId;

  const RelationshipTreeView({
    super.key,
    required this.relationshipController,
    required this.memberId,
  });

  @override
  State<RelationshipTreeView> createState() => _RelationshipTreeViewState();
}

class _RelationshipTreeViewState extends State<RelationshipTreeView> {
  late Graph graph;
  late Algorithm algorithm;
  TransformationController? transformationController;

  // Efficient lookup maps for performance
  late Map<String, MemberRelationship> _relationshipLookup;
  late Map<String, String> _memberNameLookup;
  late Map<String, String?> _memberProfileLookup;
  late Map<String, String> _relationshipTypeLookup;
  late Map<String, Color> _relationshipColorLookup;

  @override
  void initState() {
    super.initState();
    // Initialize with 0.8x scale for ultra-compact view
    final matrix = Matrix4.identity()..scale(0.8);
    transformationController = TransformationController(matrix);
    _buildLookupMaps();
    _initializeGraph();
  }

  /// Reset the graph to center and default scale
  void _centerAndScaleGraph() {
    if (transformationController == null) return;

    // Reset to 0.8x scale for ultra-compact view
    final matrix = Matrix4.identity()..scale(0.8);
    transformationController!.value = matrix;
  }

  /// Zoom in the graph
  void _zoomIn() {
    if (transformationController == null) return;

    final currentMatrix = transformationController!.value;
    final currentScale = currentMatrix.getMaxScaleOnAxis();
    final newScale = (currentScale * 1.2).clamp(0.5, 5.0);

    final matrix = Matrix4.identity()..scale(newScale);
    transformationController!.value = matrix;
  }

  /// Zoom out the graph
  void _zoomOut() {
    if (transformationController == null) return;

    final currentMatrix = transformationController!.value;
    final currentScale = currentMatrix.getMaxScaleOnAxis();
    final newScale = (currentScale / 1.2).clamp(0.5, 5.0);

    final matrix = Matrix4.identity()..scale(newScale);
    transformationController!.value = matrix;
  }

  /// Build efficient lookup maps to avoid repeated linear searches
  void _buildLookupMaps() {
    _relationshipLookup = {};
    _memberNameLookup = {};
    _memberProfileLookup = {};
    _relationshipTypeLookup = {};
    _relationshipColorLookup = {};

    final mainMember = widget.relationshipController.selectedMember.value;
    if (mainMember != null) {
      _memberNameLookup[widget.memberId] =
          '${mainMember.firstName ?? ''} ${mainMember.secondName ?? ''}'.trim();
      _memberProfileLookup[widget.memberId] = mainMember.profileUrl;
    }

    for (var relationship in widget.relationshipController.relationships) {
      if (relationship.id == null) continue;

      final fromId = relationship.fromMemberId ?? '';
      final toId = relationship.toMemberId ?? '';

      // Store relationship by both member IDs for quick lookup
      _relationshipLookup[fromId] = relationship;
      _relationshipLookup[toId] = relationship;

      // Cache member names and profiles
      final isFromMain = fromId == widget.memberId;
      final relatedMemberId = isFromMain ? toId : fromId;

      _memberNameLookup[relatedMemberId] =
          isFromMain
              ? relationship.getToMemberName()
              : relationship.getFromMemberName();

      _memberProfileLookup[relatedMemberId] =
          isFromMain
              ? relationship.getToMemberProfileUrl()
              : relationship.getFromMemberProfileUrl();

      // Cache relationship type and color
      final relationshipType =
          relationship.relationshipType?.title ?? 'Unknown';
      _relationshipTypeLookup[relatedMemberId] = relationshipType;
      _relationshipColorLookup[relatedMemberId] = getRelationshipColor(
        context,
        relationship,
      );
    }
  }

  void _initializeGraph() {
    graph = Graph()..isTree = true;

    // Create a node for the main member
    final Node mainNode = Node.Id(widget.memberId);

    // Add all relationships to the graph (let the algorithm handle positioning)
    for (var relationship in widget.relationshipController.relationships) {
      if (relationship.id == null) continue;

      final String relatedMemberId =
          relationship.fromMemberId == widget.memberId
              ? relationship.toMemberId!
              : relationship.fromMemberId!;

      final Node relatedNode = Node.Id(relatedMemberId);

      // Use cached color instead of recalculating
      final color =
          _relationshipColorLookup[relatedMemberId] ??
          Theme.of(context).colorScheme.primary;

      final paint =
          Paint()
            ..color = color
            ..strokeWidth = 3
            ..style = PaintingStyle.stroke;

      graph.addEdge(mainNode, relatedNode, paint: paint);
    }

    // Configure the tree layout algorithm with compact spacing
    final configuration =
        BuchheimWalkerConfiguration()
          ..siblingSeparation =
              120 // Reduced spacing between siblings for compact view
          ..levelSeparation =
              150 // Reduced spacing between levels for compact view
          ..subtreeSeparation =
              130 // Reduced spacing between subtrees for compact view
          ..orientation = BuchheimWalkerConfiguration.ORIENTATION_TOP_BOTTOM;

    algorithm = BuchheimWalkerAlgorithm(
      configuration,
      TreeEdgeRenderer(configuration),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Instructions for tree view with reset button
        Container(
          padding: EdgeInsets.all(8.r),
          margin: EdgeInsets.only(bottom: 8.r),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                IconlyLight.infoSquare,
                color: Theme.of(context).colorScheme.primary,
                size: 20.r,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Drag to pan, pinch or scroll to zoom. Family members are arranged hierarchically: parents above, children below, siblings to the side.',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              SizedBox(width: 8.w),
              // Zoom controls
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Zoom out button
                  InkWell(
                    onTap: _zoomOut,
                    child: Container(
                      padding: EdgeInsets.all(6.r),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Icon(
                        Icons.remove,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16.r,
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // Reset/Center button
                  InkWell(
                    onTap: _centerAndScaleGraph,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            IconlyLight.location,
                            color: Theme.of(context).colorScheme.primary,
                            size: 14.r,
                          ),
                          SizedBox(width: 3.w),
                          Text(
                            'Reset',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  // Zoom in button
                  InkWell(
                    onTap: _zoomIn,
                    child: Container(
                      padding: EdgeInsets.all(6.r),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Icon(
                        Icons.add,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16.r,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Tree visualization
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: InteractiveViewer(
              transformationController: transformationController,
              constrained: false,
              boundaryMargin: EdgeInsets.all(100), // Standard boundary margin
              minScale: 0.5, // Reasonable minimum scale
              maxScale: 5.0, // Reasonable maximum scale
              // Set initial scale to make graph larger
              child: GraphView(
                graph: graph,
                algorithm: algorithm,
                paint:
                    Paint()
                      ..color = Theme.of(context).colorScheme.primary
                      ..strokeWidth = 3
                      ..style = PaintingStyle.stroke,
                builder: (Node node) {
                  // Build node widget based on member ID using cached data
                  return _buildMemberNodeOptimized(node);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Optimized node builder using cached data
  Widget _buildMemberNodeOptimized(Node node) {
    final String memberId = node.key?.value?.toString() ?? '';
    final bool isMainMember = memberId == widget.memberId;

    // Get cached data
    final memberName = _memberNameLookup[memberId] ?? 'Unknown';
    final profileUrl = _memberProfileLookup[memberId];
    final relationshipType = _relationshipTypeLookup[memberId] ?? '';
    final relationshipColor = _relationshipColorLookup[memberId];

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Profile image with border
        InkWell(
          onTap: () {
            if (widget.relationshipController.selectedMember.value != null) {}
          },
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    isMainMember
                        ? Theme.of(context).colorScheme.primary
                        : relationshipColor ??
                            Theme.of(context).colorScheme.primary,
                width: isMainMember ? 3 : 2,
              ),
            ),
            child: CircleAvatar(
              radius: 35.r, // Further reduced for more compact size
              backgroundColor:
                  isMainMember
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
              backgroundImage:
                  profileUrl != null && profileUrl.isNotEmpty
                      ? NetworkImage(profileUrl)
                      : null,
              child:
                  profileUrl == null || profileUrl.isEmpty
                      ? Icon(
                        IconlyLight.profile,
                        size: 22.r, // Reduced for compact size
                        color: Theme.of(context).colorScheme.primary,
                      )
                      : null,
            ),
          ),
        ),
        SizedBox(height: 6.h),

        // Member name
        Container(
          constraints: BoxConstraints(
            maxWidth: 130.w,
          ), // Further reduced width for compact view
          child: Text(
            memberName,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isMainMember ? FontWeight.bold : FontWeight.normal,
              fontSize: 15.sp, // Further reduced for compact view
              color:
                  isMainMember
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        // Relationship type (only for related members)
        if (!isMainMember && relationshipType.isNotEmpty)
          Container(
            constraints: BoxConstraints(
              maxWidth: 120.w,
            ), // Further reduced width for compact view
            padding: EdgeInsets.only(top: 2.h), // Reduced padding
            child: Text(
              relationshipType,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                    relationshipColor ?? Theme.of(context).colorScheme.primary,
                fontSize: 13.sp, // Further reduced for compact view
                fontWeight: FontWeight.w500, // Keep weight for visibility
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }

  @override
  void didUpdateWidget(RelationshipTreeView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Rebuild lookup maps and graph if relationships changed
    if (oldWidget.relationshipController.relationships.length !=
        widget.relationshipController.relationships.length) {
      _buildLookupMaps();
      _initializeGraph();
    }
  }

  @override
  void dispose() {
    transformationController?.dispose();
    // Clear caches to free memory
    clearRelationshipCaches();
    super.dispose();
  }
}
