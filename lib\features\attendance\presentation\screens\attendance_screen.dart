import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:pluto_grid/pluto_grid.dart';

import '../../../../core/app/constants/routes.dart';
import '../../controllers/attendance_controller.dart';
import '../widgets/attendance_filter_widget.dart';
import '../../models/attendance_model.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final AttendanceController _attendanceController =
      Get.find<AttendanceController>();
  final logger = Get.find<Logger>();

  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  // Setup rows for the PlutoGrid
  List<PlutoRow> setupRows(List<AttendanceModel> attendanceList) {
    return attendanceList.map((attendance) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: attendance.id ?? ''),
          'attendee_name': PlutoCell(value: attendance.attendeeName ?? 'N/A'),
          'attendee_email': PlutoCell(value: attendance.attendeeEmail ?? 'N/A'),
          'attendee_phone': PlutoCell(value: attendance.attendeePhone ?? 'N/A'),
          'time_in': PlutoCell(
            value:
                attendance.timeIn != null
                    ? _attendanceController.formatDateTime(attendance.timeIn!)
                    : 'N/A',
          ),
          'time_out': PlutoCell(
            value:
                attendance.timeOut != null
                    ? _attendanceController.formatDateTime(attendance.timeOut!)
                    : 'N/A',
          ),
          'location': PlutoCell(value: attendance.locationName ?? 'N/A'),
          'created_by': PlutoCell(value: attendance.createdByUserName),
          'is_visitor': PlutoCell(
            value: attendance.isVisitor == true ? 'Yes' : 'No',
          ),
          'actions': PlutoCell(value: attendance.id),
        },
      );
    }).toList();
  }

  // Set up columns for the PlutoGrid
  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
        hide: true, // Hidden but available for filtering
      ),
      PlutoColumn(
        title: 'Attendee Name',
        field: 'attendee_name',
        type: PlutoColumnType.text(),
        width: 180,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'attendee_email',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'attendee_phone',
        type: PlutoColumnType.text(),
        width: 140,
      ),
      PlutoColumn(
        title: 'Clock In',
        field: 'time_in',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Clock Out',
        field: 'time_out',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Location',
        field: 'location',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Created By',
        field: 'created_by',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Visitor',
        field: 'is_visitor',
        type: PlutoColumnType.text(),
        width: 80,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 180,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(
                  Icons.access_time,
                  color: Colors.orange,
                  size: 20,
                ),
                onPressed: () {
                  final attendance = _attendanceController.attendanceList
                      .firstWhereOrNull(
                        (a) => a.id == rendererContext.cell.value,
                      );
                  if (attendance != null) {
                    _showTimeoutConfirmation(attendance);
                  }
                },
                tooltip: 'Mark Timeout',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
              IconButton(
                icon: const Icon(Icons.edit, color: Colors.blue, size: 20),
                onPressed: () {
                  final attendanceId =
                      rendererContext.cell.value as String? ?? '';
                  if (attendanceId.isNotEmpty) {
                    context.go(
                      Routes.EDIT_ATTENDANCE.replaceFirst(':id', attendanceId),
                    );
                  }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
              IconButton(
                icon: const Icon(
                  Icons.visibility,
                  color: Colors.green,
                  size: 20,
                ),
                onPressed: () {
                  final attendanceId =
                      rendererContext.cell.value as String? ?? '';
                  if (attendanceId.isNotEmpty) {
                    context.go(
                      Routes.VIEW_ATTENDANCE.replaceFirst(':id', attendanceId),
                    );
                  }
                },
                tooltip: 'View',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
            ],
          );
        },
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.go(Routes.MARK_ATTENDANCE);
            },
            tooltip: 'Mark Attendance',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Filter widget
            const AttendanceFilterWidget(),

            // Attendance grid
            Obx(() {
              if (_attendanceController.isLoading.value) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              rows = setupRows(_attendanceController.attendanceList);

              return Container(
                padding: const EdgeInsets.all(16),
                height: 600.h,
                child: Column(
                  children: [
                    Expanded(
                      child: PlutoGrid(
                        columns: columns,
                        rows: rows,
                        onLoaded: (PlutoGridOnLoadedEvent event) {
                          stateManager = event.stateManager;
                        },
                        configuration: PlutoGridConfiguration(
                          columnFilter: PlutoGridColumnFilterConfig(
                            filters: const [...FilterHelper.defaultFilters],
                          ),
                        ),
                      ),
                    ),

                    // Pagination controls
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.first_page),
                            onPressed:
                                _attendanceController.isFirstPage.value
                                    ? null
                                    : () => _attendanceController.goToPage(0),
                            tooltip: 'First Page',
                          ),
                          IconButton(
                            icon: const Icon(Icons.chevron_left),
                            onPressed:
                                _attendanceController.isFirstPage.value
                                    ? null
                                    : () =>
                                        _attendanceController.previousPage(),
                            tooltip: 'Previous Page',
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                            ),
                            child: Obx(
                              () => Text(
                                'Page ${_attendanceController.currentPage.value + 1} of ${_attendanceController.totalPages.value}',
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.chevron_right),
                            onPressed:
                                _attendanceController.isLastPage.value
                                    ? null
                                    : () => _attendanceController.nextPage(),
                            tooltip: 'Next Page',
                          ),
                          IconButton(
                            icon: const Icon(Icons.last_page),
                            onPressed:
                                _attendanceController.isLastPage.value
                                    ? null
                                    : () => _attendanceController.goToPage(
                                      _attendanceController.totalPages.value -
                                          1,
                                    ),
                            tooltip: 'Last Page',
                          ),
                        ],
                      ),
                    ),

                    // Total items count
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Obx(
                        () => Text(
                          'Total: ${_attendanceController.totalItems.value} items',
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  // Show timeout confirmation dialog
  void _showTimeoutConfirmation(AttendanceModel attendance) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Mark Timeout'),
          content: const Text(
            'Are you sure you want to mark timeout for this attendance record?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                _attendanceController.markTimeout(attendance);
              },
              child: const Text('Mark Timeout'),
            ),
          ],
        );
      },
    );
  }
}
