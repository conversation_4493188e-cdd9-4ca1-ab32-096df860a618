import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:logger/logger.dart';

import '../../models/staff_model.dart';

import '../../controllers/staff_controller.dart';
import '../widgets/staff_filter_widget.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import 'package:onechurch/features/staff_roles/integration/staff_integration.dart';

class StaffScreen extends StatefulWidget {
  const StaffScreen({super.key});

  @override
  State<StaffScreen> createState() => _StaffScreenState();
}

class _StaffScreenState extends State<StaffScreen> {
  final StaffController controller = Get.find<StaffController>();
  final logger = Get.find<Logger>();

  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  // Set up columns for the PlutoGrid
  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
        hide: true, // Hidden but available for filtering
      ),
      PlutoColumn(
        title: 'Name',
        field: 'name',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'phone',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'email',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'ID Number',
        field: 'idNumber',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.cell.value as String? ?? 'unknown';
          Color chipColor;
          switch (status.toLowerCase()) {
            case 'active':
              chipColor = Colors.green;
              break;
            case 'inactive':
              chipColor = Colors.grey;
              break;
            case 'pending':
              chipColor = Colors.orange;
              break;
            default:
              chipColor = Colors.blue;
          }
          return Container(
            decoration: BoxDecoration(
              color: chipColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Text(
              status,
              style: TextStyle(color: chipColor, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 250,
        renderer: (rendererContext) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      IconlyLight.edit,
                      color: Colors.blue,
                      size: 18,
                    ),
                    onPressed: () {
                      final staffId =
                          rendererContext.row.cells['id']!.value as String? ??
                          '';
                      if (staffId.isNotEmpty) {
                        _editStaff(context, staffId);
                      }
                    },
                    tooltip: 'Edit Staff',
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
                const SizedBox(width: 4),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.manage_accounts,
                      color: Colors.green,
                      size: 18,
                    ),
                    onPressed: () {
                      final staffId =
                          rendererContext.row.cells['id']!.value as String? ??
                          '';
                      if (staffId.isNotEmpty) {
                        _assignRoles(context, staffId);
                      }
                    },
                    tooltip: 'Assign Roles',
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
                const SizedBox(width: 4),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      IconlyLight.delete,
                      color: Colors.red,
                      size: 18,
                    ),
                    onPressed: () {
                      final staffId =
                          rendererContext.row.cells['id']!.value as String? ??
                          '';
                      if (staffId.isNotEmpty) {
                        _confirmDelete(context, staffId);
                      }
                    },
                    tooltip: 'Delete Staff',
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ];
  }

  // Setup rows for the PlutoGrid
  List<PlutoRow> setupRows(List<dynamic> staffs) {
    return staffs.map((staff) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: staff.id),
          'name': PlutoCell(
            value: '${staff.firstName ?? ''} ${staff.secondName ?? ''}',
          ),
          'phone': PlutoCell(value: staff.phoneNumber ?? ''),
          'email': PlutoCell(value: staff.email ?? ''),
          'idNumber': PlutoCell(value: staff.idNumber ?? ''),
          'status': PlutoCell(value: staff.status ?? 'unknown'),
          'actions': PlutoCell(value: staff.id),
        },
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Staff Management'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: StaffFilterWidget().buildTextFilter(
                context,
                controller,
                label: 'Search (Phone, Email, ID, Code)',
                icon: IconlyLight.search,
                onChanged: (value) => controller.setIdentifierFilter(value),
                initialValue: controller.identifierFilter.value,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? const Icon(Icons.filter_alt)
                      : const Icon(Icons.filter_alt_outlined),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateStaff(context),
        tooltip: 'Create new staff',
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Filters section
          Obx(
            () =>
                showFilter.value ? const StaffFilterWidget() : const SizedBox(),
          ),
          Gap(8.h),

          // Stats and info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Obx(
                  () => Text(
                    "Total Staff: ${controller.totalItems}",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                const Spacer(),
                Obx(
                  () =>
                      controller.isLoading.value
                          ? const CircularProgressIndicator()
                          : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          Gap(8.h),

          // Error message if any
          Obx(
            () =>
                controller.errorMessage.value.isNotEmpty
                    ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Error: ${controller.errorMessage.value}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),

          // Staff grid
          Expanded(
            child: Card(
              color: Theme.of(context).secondaryHeaderColor,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 4,
              child: PlutoGrid(
                mode: PlutoGridMode.selectWithOneTap,
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  stateManager = event.stateManager;
                  stateManager.setShowColumnFilter(true);
                  if (kDebugMode) {
                    debugPrint("Grid loaded");
                  }
                },
                onSelected: (event) {
                  if (kDebugMode) {
                    debugPrint("Selected: ${event.row?.cells['id']?.value}");
                  }
                },
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    activatedColor: const Color.fromARGB(255, 165, 205, 253),
                    cellTextStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(255, 64, 64, 64),
                    ),
                    columnTextStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blueGrey,
                    ),
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                    resizeMode: PlutoResizeMode.normal,
                  ),
                ),
                createFooter: (stateManager) {
                  return PlutoLazyPagination(
                    initialPage: 0,
                    initialFetch: true,
                    fetchWithSorting: true,
                    fetchWithFiltering: true,
                    pageSizeToMove: null,
                    fetch: (pagReq) async {
                      controller.currentPage.value = pagReq.page;
                      debugPrint("Fetching page: ${pagReq.page}");

                      await controller.fetchStaffs();
                      if (controller.errorMessage.value.isNotEmpty) {
                        ToastUtils.showErrorToast(
                          controller.errorMessage.value,
                          null,
                        );
                      }

                      return Future.value(
                        PlutoLazyPaginationResponse(
                          totalPage: controller.totalPages.value,
                          rows: setupRows(controller.staffs),
                        ),
                      );
                    },
                    stateManager: stateManager,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateStaff(BuildContext context) {
    context.go(Routes.CREATE_STAFF);
  }

  void _editStaff(BuildContext context, String id) {
    try {
      // Find the staff in the current table data
      final staffModel = controller.staffs.firstWhere(
        (staff) => staff.id == id,
        orElse: () => StaffModel(), // Return empty model if not found
      );

      if (staffModel.id != null) {
        // Navigate to edit screen with the staff ID and model
        context.push(
          Routes.EDIT_STAFF.replaceFirst(':id', id),
          extra: staffModel,
        );
      } else {
        ToastUtils.showErrorToast(
          'Staff not found',
          'Unable to find staff with ID: $id',
        );
      }
    } catch (e) {
      // Show error message
      ToastUtils.showErrorToast('Failed to load staff data', e.toString());
      logger.e('Error loading staff data: $e');
    }
  }

  void _assignRoles(BuildContext context, String id) {
    try {
      // Find the staff model from the list
      final staffModel = controller.staffs.firstWhere(
        (staff) => staff.id == id,
        orElse: () => StaffModel(), // Return empty model if not found
      );

      if (staffModel.id != null) {
        // Navigate to role assignment screen using the integration method
        StaffRolesIntegration.navigateToStaffRoleAssignments(
          context,
          staffModel,
        );
      } else {
        ToastUtils.showErrorToast(
          'Staff not found',
          'Unable to find staff with ID: $id',
        );
      }
    } catch (e) {
      // Show error message
      ToastUtils.showErrorToast(
        'Error navigating to role assignment screen',
        e.toString(),
      );
      logger.e('Error navigating to role assignment screen: $e');
    }
  }

  void _confirmDelete(BuildContext context, String id) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text(
              'Are you sure you want to delete this staff member?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteStaff(id);
                },
                style: FilledButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _deleteStaff(String id) async {
    final result = await controller.deleteStaff(id);
    if (result) {
      ToastUtils.showSuccessToast(
        'Success',
        'Staff member deleted successfully',
      );
    } else {
      ToastUtils.showErrorToast('Error', controller.errorMessage.value);
    }
  }
}
