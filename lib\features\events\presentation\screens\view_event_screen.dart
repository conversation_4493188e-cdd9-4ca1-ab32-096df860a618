import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../../core/app/constants/routes.dart';
import '../../../../data/models/event_model.dart';
import '../../controllers/event_controller.dart';
import '../widgets/event_attendance_widget.dart';

class ViewEventScreen extends StatefulWidget {
  final String eventId;
  final EventModel? initialEvent;

  const ViewEventScreen({super.key, required this.eventId, this.initialEvent});

  @override
  State<ViewEventScreen> createState() => _ViewEventScreenState();
}

class _ViewEventScreenState extends State<ViewEventScreen>
    with SingleTickerProviderStateMixin {
  final EventController _eventController = Get.find<EventController>();
  EventModel? _event;
  bool _isLoading = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    if (widget.initialEvent != null) {
      _event = widget.initialEvent;
    } else {
      _fetchEventById();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchEventById() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final event = await _eventController.getEventById(widget.eventId);
      if (event != null) {
        setState(() {
          _event = event;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event not found'),
            backgroundColor: Colors.red,
          ),
        );
        context.go('/events');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load event: $e'),
          backgroundColor: Colors.red,
        ),
      );
      context.go('/events');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    try {
      return DateFormat('dd MMM yyyy, HH:mm').format(date);
    } catch (e) {
      return 'N/A';
    }
  }

  String _formatDateOnly(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return 'N/A';
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateStr;
    }
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          Gap(12.w),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    switch (status.toLowerCase()) {
      case 'active':
        chipColor = Colors.green;
        break;
      case 'inactive':
        chipColor = Colors.grey;
        break;
      case 'draft':
        chipColor = Colors.orange;
        break;
      default:
        chipColor = Colors.blue;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: chipColor.withOpacity(0.5)),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: chipColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildEventDetailsTab() {
    final theme = Theme.of(context);
    final event = _event!;

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event Header Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              event.title ?? 'Untitled Event',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Gap(8.h),
                            Row(
                              children: [
                                Icon(IconlyLight.location, size: 16),
                                Gap(4.w),
                                Expanded(
                                  child: Text(
                                    event.location ?? 'No location specified',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      _buildStatusChip(event.status ?? 'unknown'),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Gap(16.h),

          // Event Dates Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Event Schedule',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Gap(16.h),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  IconlyLight.calendar,
                                  size: 16,
                                  color: Colors.green,
                                ),
                                Gap(8.w),
                                Text(
                                  'Start Date',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                            Gap(4.h),
                            Text(
                              _formatDateOnly(event.startDate),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  IconlyLight.calendar,
                                  size: 16,
                                  color: Colors.red,
                                ),
                                Gap(8.w),
                                Text(
                                  'End Date',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                            Gap(4.h),
                            Text(
                              _formatDateOnly(event.endDate),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(16.h),
                  _buildDetailRow(
                    'Frequency',
                    event.frequency ?? 'N/A',
                    IconlyLight.timeCircle,
                  ),
                ],
              ),
            ),
          ),
          Gap(16.h),

          // Description Card
          if (event.description != null && event.description!.isNotEmpty) ...[
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Description',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Gap(16.h),
                    Html(
                      data: event.description!,
                      style: {
                        "body": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                      },
                    ),
                  ],
                ),
              ),
            ),
            Gap(16.h),
          ],

          // Event Details Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Event Details',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Gap(16.h),
                  _buildDetailRow(
                    'Created At',
                    _formatDate(event.createdAt),
                    IconlyLight.timeCircle,
                  ),
                  _buildDetailRow(
                    'Last Updated',
                    _formatDate(event.updatedAt),
                    IconlyLight.edit,
                  ),
                  _buildDetailRow(
                    'Created By',
                    event.createdByUser?.firstName != null &&
                            event.createdByUser?.secondName != null
                        ? '${event.createdByUser!.firstName} ${event.createdByUser!.secondName}'
                        : event.createdByUser?.firstName ??
                            event.createdByUser?.email ??
                            'Unknown',
                    IconlyLight.profile,
                  ),
                  if (event.id != null)
                    _buildDetailRow(
                      'Event ID',
                      event.id!,
                      IconlyLight.document,
                    ),
                ],
              ),
            ),
          ),
          Gap(24.h),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => context.go('/events'),
                  icon: const Icon(IconlyLight.arrowLeft),
                  label: const Text('Back to Events'),
                ),
              ),
              Gap(16.w),
              Expanded(
                child: FilledButton.icon(
                  onPressed: () {
                    context.go(
                      '${Routes.EVENTS}/${widget.eventId}/edit',
                      extra: event,
                    );
                  },
                  icon: const Icon(IconlyLight.edit),
                  label: const Text('Edit Event'),
                ),
              ),
            ],
          ),
          Gap(24.h),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Event Details')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_event == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Event Details')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(IconlyLight.calendar, size: 48),
              const SizedBox(height: 16),
              Text('Event not found', style: theme.textTheme.titleMedium),
              TextButton(
                onPressed: () => context.go('/events'),
                child: const Text('Go back'),
              ),
            ],
          ),
        ),
      );
    }

    final event = _event!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Details'),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.edit),
            onPressed: () {
              context.go(
                '${Routes.EVENTS}/${widget.eventId}/edit',
                extra: event,
              );
            },
            tooltip: 'Edit Event',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(IconlyLight.document), text: 'Details'),
            Tab(icon: Icon(IconlyLight.user3), text: 'Attendance'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEventDetailsTab(),
          EventAttendanceWidget(eventId: widget.eventId, event: event),
        ],
      ),
    );
  }
}
