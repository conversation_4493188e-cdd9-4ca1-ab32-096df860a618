import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../controllers/member_controller.dart';

class MemberFilterWidget extends StatelessWidget {
  const MemberFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MemberController>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(controller, context),
            const Divider(height: 24),
            _buildFilter<PERSON>ont<PERSON>s(controller, context),
            const SizedBox(height: 16),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(MemberController controller, BuildContext context) {
    return Row(
      children: [
        Icon(IconlyLight.filter, color: Theme.of(context).primaryColor),
        const SizedBox(width: 8),
        Text(
          'Filter Members',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(controller, context),
      ],
    );
  }

  Widget _buildClearButton(MemberController controller, BuildContext context) {
    return TextButton(
      onPressed: controller.clearFilters,
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(
    MemberController controller,
    BuildContext context,
  ) {
    return SizedBox(
      child: SingleChildScrollView(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (MediaQuery.of(context).size.width < 600) ...[
              SizedBox(
                width: 180.w,
                child: buildSearchField(controller, context),
              ),
              const SizedBox(width: 8),
            ],
            // SizedBox(width: 150, child: _buildIdField(controller, context)),
            // const SizedBox(width: 8),
            SizedBox(
              width: 110.w,
              child: _buildDateField(context, controller, isStartDate: true),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 110.w,
              child: _buildDateField(context, controller, isStartDate: false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    MemberController controller, {
    required bool isStartDate,
  }) {
    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final label = isStartDate ? 'Joined Date' : 'Date of Birth';
      final icon = IconlyLight.calendar;

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(icon),
        suffixIcon: _buildClearIcon(
          () =>
              isStartDate
                  ? controller.setDateFilters(null, controller.endDate.value)
                  : controller.setDateFilters(controller.startDate.value, null),
          showClear: date != null,
        ),
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context, controller)
                    : _selectEndDate(context, controller),
      );
    });
  }

  Widget _buildActionButtons(MemberController controller) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: controller.fetchMembers,
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Future<void> _selectStartDate(
    BuildContext context,
    MemberController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      controller.setDateFilters(picked, controller.endDate.value);
    }
  }

  Future<void> _selectEndDate(
    BuildContext context,
    MemberController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.setDateFilters(controller.startDate.value, endOfDay);
    }
  }
}

Widget buildSearchField(MemberController controller, BuildContext context) {
  return CustomTextField(
    controller: controller.searchController,
    labelText: 'Search Members',
    hintText: 'Name, Email, Phone...',
    prefixIcon: Icon(IconlyLight.search),
    suffixIcon: _buildClearIcon(() => controller.searchController.clear()),
    prefix:
        MediaQuery.of(context).size.width > 600
            ? null
            : IconButton.filled(
              onPressed: () {
                controller.setSearchQuery(controller.searchController.text);
                controller.fetchMembers();
              },
              icon: const Icon(Icons.search),
            ),

    onSubmitted: (value) {
      controller.setSearchQuery(value);
      controller.fetchMembers();
    },
  );
}

Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
  return IconButton(
    icon: Icon(Icons.clear, color: Colors.grey),
    onPressed: showClear ? onPressed : null,
    splashRadius: 20,
    padding: EdgeInsets.zero,
  );
}
