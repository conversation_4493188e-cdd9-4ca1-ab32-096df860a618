{"status": true, "message": "success", "data": [{"id": "0196d91a-b972-77e6-8403-630322046f34", "created_at": "2025-05-16T14:38:49.458559+02:00", "updated_at": "2025-05-16T14:38:51.460956+02:00", "deleted_at": null, "name": "ADMIN", "description": "Full system access", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [{"id": "0196aca3-f86e-7203-8544-6f2826<PERSON><PERSON><PERSON>", "created_at": "2025-05-07T23:25:49.294156+02:00", "updated_at": "2025-05-07T23:25:49.294156+02:00", "deleted_at": null, "code": "CREATE_MEMBER", "name": "Create Member", "description": "Can create a new church member", "roles": null}, {"id": "0196aca4-0bb6-72cc-99be-0f5ca2b23cbc", "created_at": "2025-05-07T23:25:54.230206+02:00", "updated_at": "2025-05-07T23:25:54.230206+02:00", "deleted_at": null, "code": "VIEW_FINANCES", "name": "View Finances", "description": "Can view financial records", "roles": null}, {"id": "0196aca4-0e1e-7a4d-93fe-45d8e4c04762", "created_at": "2025-05-07T23:25:54.846698+02:00", "updated_at": "2025-05-07T23:25:54.846698+02:00", "deleted_at": null, "code": "MANAGE_FINANCES", "name": "Manage Finances", "description": "Can create/update financial records", "roles": null}, {"id": "0196aca4-1048-7612-aff5-c4d2bbbfffe5", "created_at": "2025-05-07T23:25:55.400423+02:00", "updated_at": "2025-05-07T23:25:55.400423+02:00", "deleted_at": null, "code": "MANAGE_ROLES", "name": "Manage Roles", "description": "Can manage user roles and permissions", "roles": null}], "assignments": []}, {"id": "0196d91a-c8e6-7382-8fd8-491fc48d49be", "created_at": "2025-05-16T14:38:53.414255+02:00", "updated_at": "2025-05-16T14:38:53.828743+02:00", "deleted_at": null, "name": "PASTOR", "description": "Access to members and events", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d91a-cedf-7de3-9c7d-53829001a67e", "created_at": "2025-05-16T14:38:54.943952+02:00", "updated_at": "2025-05-16T14:38:55.384138+02:00", "deleted_at": null, "name": "TREASURER", "description": "Manages financial operations", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d91a-d56b-78a7-9bdc-1c4745d5cc6d", "created_at": "2025-05-16T14:38:56.619607+02:00", "updated_at": "2025-05-16T14:38:57.600501+02:00", "deleted_at": null, "name": "CLERK", "description": "Manages financial operations", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d91a-b7cf-71b8-aad2-0d776b7f1504", "created_at": "2025-05-16T14:38:49.039151+02:00", "updated_at": "2025-05-16T22:27:49.255194+02:00", "deleted_at": null, "name": "ADMIN", "description": "Full system access", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [{"id": "0196aca3-f86e-7203-8544-6f2826<PERSON><PERSON><PERSON>", "created_at": "2025-05-07T23:25:49.294156+02:00", "updated_at": "2025-05-07T23:25:49.294156+02:00", "deleted_at": null, "code": "CREATE_MEMBER", "name": "Create Member", "description": "Can create a new church member", "roles": null}, {"id": "0196aca4-0bb6-72cc-99be-0f5ca2b23cbc", "created_at": "2025-05-07T23:25:54.230206+02:00", "updated_at": "2025-05-07T23:25:54.230206+02:00", "deleted_at": null, "code": "VIEW_FINANCES", "name": "View Finances", "description": "Can view financial records", "roles": null}, {"id": "0196aca4-0e1e-7a4d-93fe-45d8e4c04762", "created_at": "2025-05-07T23:25:54.846698+02:00", "updated_at": "2025-05-07T23:25:54.846698+02:00", "deleted_at": null, "code": "MANAGE_FINANCES", "name": "Manage Finances", "description": "Can create/update financial records", "roles": null}, {"id": "0196aca4-1048-7612-aff5-c4d2bbbfffe5", "created_at": "2025-05-07T23:25:55.400423+02:00", "updated_at": "2025-05-07T23:25:55.400423+02:00", "deleted_at": null, "code": "MANAGE_ROLES", "name": "Manage Roles", "description": "Can manage user roles and permissions", "roles": null}], "assignments": []}, {"id": "0196d91a-c747-7d4d-a8d4-cd024ded9b56", "created_at": "2025-05-16T14:38:52.999895+02:00", "updated_at": "2025-05-16T22:27:49.260006+02:00", "deleted_at": null, "name": "PASTOR", "description": "Access to members and events", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d91a-cd42-77b9-8849-a0476e9feef1", "created_at": "2025-05-16T14:38:54.530527+02:00", "updated_at": "2025-05-16T22:27:49.26189+02:00", "deleted_at": null, "name": "TREASURER", "description": "Manages financial operations", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d91a-d2dd-7a37-af0e-4965a27b0a19", "created_at": "2025-05-16T14:38:55.965692+02:00", "updated_at": "2025-05-16T22:27:49.263763+02:00", "deleted_at": null, "name": "CLERK", "description": "Manages financial operations", "organisation_id": null, "organisation": null, "is_general": true, "status": "ACTIVE", "permissions": [], "assignments": []}, {"id": "0196d92c-123b-722c-a90a-29f33806092f", "created_at": "2025-05-16T14:57:46.299175+02:00", "updated_at": "2025-05-16T15:21:15.522002+02:00", "deleted_at": null, "name": "TREASURER", "description": "This is a treasurer role", "organisation_id": "0196aebe-93c2-7077-9f71-10030ef5a3f5", "organisation": null, "is_general": false, "status": "ACTIVE", "permissions": [{"id": "0196aca3-f86e-7203-8544-6f2826<PERSON><PERSON><PERSON>", "created_at": "2025-05-07T23:25:49.294156+02:00", "updated_at": "2025-05-07T23:25:49.294156+02:00", "deleted_at": null, "code": "CREATE_MEMBER", "name": "Create Member", "description": "Can create a new church member", "roles": null}, {"id": "0196aca3-fb6a-7481-85d3-9128011c0a58", "created_at": "2025-05-07T23:25:50.058319+02:00", "updated_at": "2025-05-07T23:25:50.058319+02:00", "deleted_at": null, "code": "EDIT_MEMBER", "name": "Edit Member", "description": "Can edit member details", "roles": null}, {"id": "0196aca3-fe38-74c7-993c-0444144d2d66", "created_at": "2025-05-07T23:25:50.776348+02:00", "updated_at": "2025-05-07T23:25:50.776348+02:00", "deleted_at": null, "code": "DELETE_MEMBER", "name": "Delete Member", "description": "Can delete a member", "roles": null}, {"id": "0196aca4-0e1e-7a4d-93fe-45d8e4c04762", "created_at": "2025-05-07T23:25:54.846698+02:00", "updated_at": "2025-05-07T23:25:54.846698+02:00", "deleted_at": null, "code": "MANAGE_FINANCES", "name": "Manage Finances", "description": "Can create/update financial records", "roles": null}], "assignments": []}], "errors": null}