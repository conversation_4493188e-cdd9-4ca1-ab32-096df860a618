import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/features/finances/controllers/transactions_controller.dart';

class TransactionFilterWidget extends StatelessWidget {
  const TransactionFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TransactionsController>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(controller, context),
            const Divider(height: 24),
            _buildFilter<PERSON>ontrols(controller, context),
            const SizedBox(height: 16),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(TransactionsController controller, BuildContext context) {
    return Row(
      children: [
        Icon(IconlyLight.filter, color: Theme.of(context).primaryColor),
        const SizedBox(width: 8),
        Text(
          'Filter Transactions',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(controller, context),
      ],
    );
  }

  Widget _buildClearButton(
    TransactionsController controller,
    BuildContext context,
  ) {
    return TextButton(
      onPressed: controller.clearFilters,
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(
    TransactionsController controller,
    BuildContext context,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          if (MediaQuery.of(context).size.width < 600) ...[
            SizedBox(
              width: 180.w,
              child: buildSearchField(controller, context),
            ),
            const SizedBox(width: 8),
          ],
          SizedBox(
            width: 110.w,
            child: _buildDateField(context, controller, isStartDate: true),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 110.w,
            child: _buildDateField(context, controller, isStartDate: false),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 130.w,
            child: _buildAccountField(controller, context),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 130.w,
            child: _buildTransactionCodeField(controller, context),
          ),
        ],
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    TransactionsController controller, {
    required bool isStartDate,
  }) {
    final label = isStartDate ? 'Start Date' : 'End Date';
    final icon = isStartDate ? IconlyLight.calendar : IconlyLight.calendar;

    return Obx(() {
      final dateStr =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final date = dateStr.isNotEmpty ? DateTime.tryParse(dateStr) : null;

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(icon),
        suffixIcon: _buildClearIcon(
          () =>
              isStartDate
                  ? controller.setDateFilters('', controller.endDate.value)
                  : controller.setDateFilters(controller.startDate.value, ''),
          showClear: dateStr.isNotEmpty,
        ),
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context, controller)
                    : _selectEndDate(context, controller),
      );
    });
  }

  Future<void> _selectStartDate(
    BuildContext context,
    TransactionsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.setDateFilters(
        picked.toIso8601String(),
        controller.endDate.value,
      );
    }
  }

  Future<void> _selectEndDate(
    BuildContext context,
    TransactionsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      controller.setDateFilters(
        controller.startDate.value,
        picked.toIso8601String(),
      );
    }
  }

  Widget _buildAccountField(
    TransactionsController controller,
    BuildContext context,
  ) {
    return Obx(() {
      return CustomTextFormField(
        labelText: 'Account',
        prefixIcon: const Icon(IconlyLight.wallet),
        suffixIcon: _buildClearIcon(
          () => controller.setAccountFilter(''),
          showClear: controller.subAccount.value.isNotEmpty,
        ),
        hintText: 'Account Number',

        controller: TextEditingController(text: controller.subAccount.value),
        onChanged: (value) => controller.setAccountFilter(value),
      );
    });
  }

  Widget _buildTransactionCodeField(
    TransactionsController controller,
    BuildContext context,
  ) {
    return Obx(() {
      return CustomTextFormField(
        labelText: 'Transaction Code',
        prefixIcon: const Icon(IconlyLight.document),
        suffixIcon: _buildClearIcon(
          () => controller.setTransactionCodeFilter(''),
          showClear: controller.transactionCode.value.isNotEmpty,
        ),
        hintText: 'Transaction Code',

        controller: TextEditingController(
          text: controller.transactionCode.value,
        ),
        onChanged: (value) => controller.setTransactionCodeFilter(value),
      );
    });
  }

  Widget _buildActionButtons(TransactionsController controller) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: controller.getTransactions,
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
    if (!showClear) return const SizedBox.shrink();

    return IconButton(
      icon: const Icon(Icons.clear, size: 18),
      onPressed: onPressed,
      splashRadius: 20,
    );
  }

  Widget buildSearchField(
    TransactionsController controller,
    BuildContext context,
  ) {
    return CustomTextField(
      controller: controller.searchController,
      labelText: 'Search Transactions',
      hintText: 'Name, Code, Phone...',
      prefixIcon: const Icon(IconlyLight.search),
      suffixIcon: _buildClearIcon(() => controller.searchController.clear()),

      onSubmitted: (value) {
        controller.setSearchQuery(value);
        controller.getTransactions();
      },
    );
  }
}
