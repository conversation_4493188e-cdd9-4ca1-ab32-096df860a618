import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../controllers/member_categories_controller.dart';
import '../../../../data/models/member_category_model.dart';

class MemberCategoriesConfig extends StatefulWidget {
  const MemberCategoriesConfig({super.key});

  @override
  State<MemberCategoriesConfig> createState() => _MemberCategoriesConfigState();
}

class _MemberCategoriesConfigState extends State<MemberCategoriesConfig> {
  late MemberCategoriesController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(MemberCategoriesController());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onPrimary,
        title: const Text('Categories'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.fetchCategories,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCategoryFormDialog(context),
            tooltip: 'Add Category',
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(12.0.r),
            child: Column(
              children: [
                // Search bar
                CustomTextField(
                  controller: controller.searchController,
                  hintText: 'Search categories...',
                  prefixIcon: Icon(Icons.search, size: 20.r),
                  suffixIcon: Obx(() {
                    return controller.searchQuery.value.isNotEmpty
                        ? IconButton(
                          icon: Icon(Icons.clear, size: 18.r),
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            controller.searchController.clear();
                          },
                        )
                        : const SizedBox.shrink();
                  }),
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ),

          // Categories list
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.filteredCategories.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.category_outlined,
                        size: 60.r,
                        color: theme.colorScheme.primary.withOpacity(0.5),
                      ),
                      SizedBox(height: 12.h),
                      Text(
                        'No categories found',
                        style: theme.textTheme.titleMedium,
                      ),
                      SizedBox(height: 6.h),
                      Text(
                        controller.searchQuery.value.isEmpty
                            ? 'Add categories to get started'
                            : 'Try a different search term',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.separated(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                itemCount: controller.filteredCategories.length,
                separatorBuilder: (context, index) => Divider(height: 1.h),
                itemBuilder: (context, index) {
                  final category = controller.filteredCategories[index];
                  return CategoryListItem(
                    category: category,
                    onEdit: () => _showCategoryFormDialog(context, category),
                    onToggleStatus:
                        () => controller.toggleCategoryStatus(
                          category.id ?? '',
                          category.isGeneral ?? false,
                        ),
                    onDelete:
                        () => controller.deleteCategory(
                          category.id ?? '',
                          context,
                        ),
                  );
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.small(
        onPressed: () => _showCategoryFormDialog(context),
        tooltip: 'Add Category',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showCategoryFormDialog(
    BuildContext context, [
    MemberCategory? category,
  ]) {
    final isEditing = category != null;

    if (isEditing) {
      controller.prepareForEdit(category);
    } else {
      controller.titleController.clear();
      controller.descriptionController.clear();
      controller.codeController.clear();
      controller.isGeneral.value =
          true; // Set default to true as per configuration
      controller.organisationId =
          Get.find<AuthController>().currentOrg.value?.id;
    }

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: SingleChildScrollView(
              child: Container(
                width: 0.9.sw,
                padding: EdgeInsets.all(16.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isEditing ? 'Edit Category' : 'Add New Category',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 24.h),

                    // Form fields
                    CustomTextField(
                      controller: controller.titleController,
                      labelText: 'Title *',
                      hintText: 'Enter title (e.g. Residential)',
                    ),
                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: controller.descriptionController,
                      labelText: 'Description',
                      hintText: 'Enter description (e.g. Residential customer)',

                      maxLines: 3,
                    ),
                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: controller.codeController,
                      labelText: 'Code *',
                      hintText: 'Enter code (e.g. RESIDENTIAL)',

                      textCapitalization: TextCapitalization.characters,
                    ),
                    SizedBox(height: 16.h),

                    // Is General toggle with explanation
                    Obx(
                      () => SwitchListTile(
                        title: const Text('Is General'),
                        subtitle: Text(
                          'General categories are available to all organizations',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        value: controller.isGeneral.value,
                        onChanged:
                            (value) => controller.isGeneral.value = value,
                      ),
                    ),

                    SizedBox(height: 24.h),

                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        SizedBox(width: 16.w),
                        Obx(
                          () => ElevatedButton(
                            onPressed:
                                controller.isLoading.value
                                    ? null
                                    : () async {
                                      bool success = false;
                                      if (isEditing) {
                                        success = await controller
                                            .updateCategory(category.id);
                                      } else {
                                        success =
                                            await controller.addCategory();
                                      }
                                      if (success && context.mounted) {
                                        Navigator.of(context).pop();
                                      }
                                    },
                            child:
                                controller.isLoading.value
                                    ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : Text(isEditing ? 'Update' : 'Save'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }
}

class CategoryListItem extends StatelessWidget {
  final MemberCategory category;
  final VoidCallback onEdit;
  final VoidCallback onToggleStatus;
  final VoidCallback onDelete;

  const CategoryListItem({
    super.key,
    required this.category,
    required this.onEdit,
    required this.onToggleStatus,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.title ?? '',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (category.description != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          category.description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              category.code ?? '',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onPrimaryContainer,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          if (category.isGeneral ?? false)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: colorScheme.tertiaryContainer,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'General',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onTertiaryContainer,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12.w),
                Chip(
                  label: Text(
                    category.isActive ?? false ? 'Active' : 'Inactive',
                    style: TextStyle(
                      color:
                          category.isActive ?? false
                              ? Colors.white
                              : Colors.black,
                      fontSize: 12,
                    ),
                  ),
                  backgroundColor:
                      category.isActive ?? false
                          ? Colors.green
                          : Colors.grey.shade300,
                  padding: EdgeInsets.zero,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
            SizedBox(height: 12.h),

            // Action buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: onToggleStatus,
                    icon: Icon(
                      category.isActive ?? false
                          ? Icons.toggle_off
                          : Icons.toggle_on,
                      size: 16.r,
                    ),
                    label: Text(
                      category.isActive ?? false ? 'Deactivate' : 'Activate',
                      style: TextStyle(fontSize: 13.sp),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor:
                          category.isActive ?? false
                              ? Colors.orange
                              : Colors.green,
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),

                  TextButton.icon(
                    onPressed: onEdit,
                    icon: Icon(Icons.edit, size: 16.r),
                    label: Text('Edit', style: TextStyle(fontSize: 13.sp)),
                    style: TextButton.styleFrom(
                      foregroundColor: colorScheme.primary,
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),

                  TextButton.icon(
                    onPressed: onDelete,
                    icon: Icon(Icons.delete, size: 16.r),
                    label: Text('Delete', style: TextStyle(fontSize: 13.sp)),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
