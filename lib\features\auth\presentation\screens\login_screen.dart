import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onechurch/core/app/translations/app_translations.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/auth_controller.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_phone_input.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final AuthController _authController = Get.find<AuthController>();
  bool _isEmailLogin = false;

  @override
  void dispose() {
    // Clear the text controllers when this screen is disposed
    _authController.emailController.clear();
    _authController.passwordController.clear();
    _authController.phoneController.clear();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      FocusScope.of(context).unfocus();
      bool success = await _authController.login(context);
      if (!success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_authController.errorMessage.value),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToForgotPassword() {
    context.go(Routes.FORGOT_PASSWORD);
  }

  bool _isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= 1100;
  bool _isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= 650 &&
      MediaQuery.of(context).size.width < 1100;
  bool _isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < 650;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDesktop = _isDesktop(context);
    final isTablet = _isTablet(context);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: Row(
          children: [
            if (isDesktop || isTablet)
              Expanded(
                flex: isDesktop ? 6 : 5,
                child: Container(
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.r),
                      bottomRight: Radius.circular(20.r),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.all(15.r),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          IconlyBold.home,
                          size: 40,
                          color: colorScheme.primary,
                        ),
                      ),
                      SizedBox(height: 30.h),
                      Text(
                        'OneChurch',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 15.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40.w),
                        child: Text(
                          'Login to access your account and manage your church community',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Right side with login form
            Expanded(
              flex: isDesktop ? 4 : (isTablet ? 5 : 10),
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(
                    isDesktop ? 60.r : (isTablet ? 40.r : 24.r),
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Only show logo on mobile
                        if (_isMobile(context)) ...[
                          SizedBox(height: 40.h),
                          Center(
                            child: Container(
                              padding: EdgeInsets.all(15.r),
                              decoration: BoxDecoration(
                                color: colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                IconlyBold.home,
                                size: 40,
                                color: colorScheme.onPrimary,
                              ),
                            ),
                          ),
                          SizedBox(height: 30.h),
                        ],

                        // Welcome text
                        Center(
                          child: Text(
                            context.tr('welcome_to_onechurch'),
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Center(
                          child: Text(
                            context.tr('sign_in_to_continue'),
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 40.h),
                        // Login form fields
                        if (!_isEmailLogin)
                          CustomPhoneInput(
                            label: 'Phone Number',
                            controller: _authController.phoneController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your phone number';
                              }
                              return null;
                            },
                            onPhoneNumberChanged: (phone) {
                              _authController.phoneController.text =
                                  phone.phoneNumber ?? '';
                            },
                          )
                        else
                          CustomTextField(
                            label: 'Email',
                            controller: _authController.emailController,
                            keyboardType: TextInputType.emailAddress,
                            prefixIcon: Icon(IconlyLight.message),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your email';
                              }
                              if (!RegExp(
                                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                              ).hasMatch(value)) {
                                return 'Please enter a valid email';
                              }
                              return null;
                            },
                          ),
                        SizedBox(height: 10.h),
                        Center(
                          child: TextButton(
                            onPressed: () {
                              setState(() {
                                _isEmailLogin = !_isEmailLogin;
                              });
                            },
                            child: Text(
                              _isEmailLogin
                                  ? 'Login with Phone'
                                  : 'Login with Email',
                              style: TextStyle(
                                fontSize: 14,
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        CustomTextField(
                          label: context.tr('password'),
                          controller: _authController.passwordController,
                          isPassword: true,
                          prefixIcon: Icon(IconlyLight.lock),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return context.tr('please_enter_password');
                            }
                            if (value.length < 6) {
                              return context.tr('password_length_error');
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 15.h),
                        Align(
                          alignment: Alignment.centerRight,
                          child: GestureDetector(
                            onTap: _navigateToForgotPassword,
                            child: Text(
                              context.tr('forgot_password'),
                              style: TextStyle(
                                fontSize: 14,
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        Obx(
                          () =>
                              _authController.errorMessage.isNotEmpty
                                  ? Padding(
                                    padding: EdgeInsets.only(bottom: 10.h),
                                    child: Text(
                                      _authController.errorMessage.value,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: colorScheme.error,
                                      ),
                                    ),
                                  )
                                  : const SizedBox.shrink(),
                        ),
                        SizedBox(height: 10.h),
                        Obx(
                          () => CustomButton(
                            text: context.tr('sign_in'),
                            isLoading: _authController.isLoading.value,
                            onPressed: _login,
                          ),
                        ),
                        SizedBox(height: 20.h),
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.2,
                                ),
                                thickness: 1.r,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: Text(
                                context.tr('or'),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: colorScheme.onSurface.withValues(
                                    alpha: 0.6,
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.2,
                                ),
                                thickness: 1.r,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20.h),
                        Obx(
                          () => CustomButton(
                            text: context.tr('sign_in_with_google'),
                            isLoading: _authController.isGoogleLoading.value,
                            onPressed:
                                () => _authController.signInWithGoogle(context),
                            backgroundColor: Colors.white,
                            textColor: Colors.black87,
                            icon: Image.asset(
                              'assets/icons/google.png',
                              height: 24,
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),

                        // Sign up text
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.tr('dont_have_account'),
                              style: TextStyle(
                                fontSize: 14,
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.6,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () => context.go(Routes.SIGNUP),
                              child: Text(
                                context.tr('sign_up'),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
