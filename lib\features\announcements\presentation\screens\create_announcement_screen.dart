import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_autocomplete.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import '../../controllers/announcement_controller.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/size_config.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/features/media_upload/media_upload.dart';

class CreateAnnouncementScreen extends StatefulWidget {
  const CreateAnnouncementScreen({super.key});

  @override
  State<CreateAnnouncementScreen> createState() =>
      _CreateAnnouncementScreenState();
}

class _CreateAnnouncementScreenState extends State<CreateAnnouncementScreen> {
  final controller = Get.find<AnnouncementController>();
  final _formKey = GlobalKey<FormState>();
  String _description = '';

  @override
  void initState() {
    super.initState();
    controller.clearFormFields();
    controller.clearMultipleAnnouncements();
  }

  Future<void> _addAnnouncementToList() async {
    if (_formKey.currentState!.validate()) {
      if (_description.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a description')),
        );
        return;
      }

      controller.addAnnouncementToList(
        title: controller.titleFormController.text,
        description: _description,
        category:
            controller.categoryController.text.isEmpty
                ? null
                : controller.categoryController.text.trim(),
        locationName:
            controller.locationNameController.text.isEmpty
                ? null
                : controller.locationNameController.text,
        status: controller.statusFormValue.value,
        media:
            controller.mediaItems.isNotEmpty
                ? controller.mediaItems.toList()
                : null,
      );

      // Clear form for next announcement
      controller.clearFormFields();
      setState(() {
        _description = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Announcement added to list (${controller.multipleAnnouncements.length} total)',
          ),
        ),
      );
    }
  }

  Future<void> _submitAllAnnouncements() async {
    if (controller.multipleAnnouncements.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one announcement')),
      );
      return;
    }

    final success = await controller.createMultipleAnnouncements();
    if (success && mounted) {
      context.go(Routes.ANNOUNCEMENTS);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Announcements'),
        actions: [
          Obx(
            () =>
                controller.isSubmittingMultiple.value
                    ? const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                    )
                    : TextButton.icon(
                      onPressed: _submitAllAnnouncements,
                      icon: const Icon(IconlyLight.upload),
                      label: Obx(
                        () => Text(
                          'Submit All (${controller.multipleAnnouncements.length})',
                        ),
                      ),
                    ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(
            horizontal: SizeConfig.screenWidth * 0.03,
            vertical: SizeConfig.screenHeight * 0.02,
          ),
          child: SizedBox(
          width: 250.w,
            child: Column(
              children: [
                // General Title Card (Separate)
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Announcement Group",
                          style: theme.textTheme.titleLarge,
                        ),
                        Gap(16.h),
                        Text(
                          "General Title *",
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Gap(8.h),
                        CustomTextFormField(
                          controller: controller.generalTitleController,
                          hintText: 'Enter general title for announcement group',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a general title';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
            
                Gap(20.h),
            
                // Announcement Form Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 20.h,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Add New Announcement Item",
                            style: theme.textTheme.titleLarge,
                          ),
                          Gap(16.h),
            
                          // Title Field
                          Text(
                            "Announcement Title *",
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Gap(8.h),
                          CustomTextFormField(
                            controller: controller.titleFormController,
                            hintText: 'Enter announcement title',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a title';
                              }
                              return null;
                            },
                          ),
                          Gap(16.h),
            
                          // Category Field
                          Text(
                            "Category",
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Gap(8.h),
                          CustomAutocomplete(
                            options: controller.itemCategories,
                            controller: controller.categoryController,
                            hintText: 'Enter or select announcement category',
                            onSelected: (value) {},
                          ),
            
                          Gap(16.h),
            
                          // Location Name Field
                          Text(
                            "Location (Optional)",
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Gap(8.h),
                          CustomTextFormField(
                            controller: controller.locationNameController,
                            hintText: 'Enter location (e.g., church hall)',
                          ),
                          Gap(16.h),
            
                          // Description Field with Markup Editor
                          Obx(() {
                            // Check if we're editing and need to set initial content
                            if (controller.editingDescription.value.isNotEmpty) {
                              _description = controller.editingDescription.value;
                              // Clear the editing description after setting it
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                controller.editingDescription.value = '';
                              });
                            }
            
                            return MarkupEditorWidget(
                              label: "Description",
                              isRequired: true,
                              hintText:
                                  "Enter announcement description with rich formatting",
                              height: 250.h,
                              onChanged: (content) {
                                _description = content;
                              },
                            );
                          }),
                          Gap(16.h),
            
                          // Media Upload Section
                          Card(
                            elevation: 0,
                            color: Colors.grey.shade50,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(IconlyLight.image),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Media Items (Optional)',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Gap(12.h),
                                  MediaUploadWidget(
                                    category: 'ANNOUNCEMENT',
                                    multipleSelect: true,
                                    onMediaSelected: (selectedMedia) {
                                      controller.mediaItems.value = selectedMedia;
                                    },
                                  ),
                                  Gap(12.h),
                                  Obx(
                                    () =>
                                        controller.mediaItems.isEmpty
                                            ? Center(
                                              child: Padding(
                                                padding: const EdgeInsets.all(
                                                  16.0,
                                                ),
                                                child: Text(
                                                  'No media items added yet',
                                                  style: TextStyle(
                                                    color: Colors.grey.shade600,
                                                  ),
                                                ),
                                              ),
                                            )
                                            : ListView.builder(
                                              shrinkWrap: true,
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  controller.mediaItems.length,
                                              itemBuilder: (context, index) {
                                                final item =
                                                    controller.mediaItems[index];
                                                return ListTile(
                                                  contentPadding: EdgeInsets.zero,
                                                  leading: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(4),
                                                    child: Image.network(
                                                      item.mediaUrl ?? '',
                                                      width: 40,
                                                      height: 40,
                                                      fit: BoxFit.cover,
                                                      errorBuilder:
                                                          (
                                                            context,
                                                            error,
                                                            stackTrace,
                                                          ) => Container(
                                                            width: 40,
                                                            height: 40,
                                                            color:
                                                                Colors
                                                                    .grey
                                                                    .shade300,
                                                            child: const Icon(
                                                              Icons.error,
                                                              size: 20,
                                                            ),
                                                          ),
                                                    ),
                                                  ),
                                                  title: Text(
                                                    item.title ?? 'Media Item',
                                                  ),
                                                  subtitle: Text(
                                                    item.mediaUrl ?? '',
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  trailing: IconButton(
                                                    icon: const Icon(
                                                      Icons.delete,
                                                      color: Colors.red,
                                                    ),
                                                    onPressed:
                                                        () => controller
                                                            .removeMediaItem(
                                                              index,
                                                            ),
                                                    tooltip: 'Remove',
                                                  ),
                                                );
                                              },
                                            ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Gap(16.h),
            
                          // Action Buttons
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    controller.clearFormFields();
                                    setState(() {
                                      _description = '';
                                    });
                                  },
                                  icon: const Icon(IconlyLight.delete),
                                  label: const Text('Clear Form'),
                                ),
                              ),
                              Gap(16.w),
                              Expanded(
                                child: FilledButton.icon(
                                  onPressed: _addAnnouncementToList,
                                  icon: const Icon(IconlyLight.plus),
                                  label: const Text('Add to List'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            
                Gap(20.h),
            
                // Announcements List Card
                Obx(
                  () =>
                      controller.multipleAnnouncements.isNotEmpty
                          ? Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        'Announcement Items to Submit',
                                        style: theme.textTheme.titleMedium,
                                      ),
                                      const Spacer(),
                                      Text(
                                        '${controller.multipleAnnouncements.length} items',
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              color: theme.primaryColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ],
                                  ),
                                  Gap(16.h),
                                  ListView.separated(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount:
                                        controller.multipleAnnouncements.length,
                                    separatorBuilder:
                                        (context, index) => const Divider(),
                                    itemBuilder: (context, index) {
                                      final announcement =
                                          controller.multipleAnnouncements[index];
                                      return InkWell(
                                        onTap:
                                            () => controller.editAnnouncementItem(
                                              index,
                                            ),
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                            vertical: 8.h,
                                            horizontal: 12.w,
                                          ),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: Colors.grey.shade300,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Text(
                                                      announcement['title'] ??
                                                          'Untitled',
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 14,
                                                      ),
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                    Gap(4.h),
                                                    Text(
                                                      announcement['description']
                                                              ?.toString()
                                                              .replaceAll(
                                                                RegExp(
                                                                  r'<[^>]*>',
                                                                ),
                                                                '',
                                                              ) ??
                                                          '',
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Colors.grey.shade600,
                                                      ),
                                                    ),
                                                    if (announcement['category'] !=
                                                            null ||
                                                        announcement['location_name'] !=
                                                            null)
                                                      Gap(2.h),
                                                    Row(
                                                      children: [
                                                        if (announcement['category'] !=
                                                            null)
                                                          Container(
                                                            padding:
                                                                EdgeInsets.symmetric(
                                                                  horizontal: 6.w,
                                                                  vertical: 2.h,
                                                                ),
                                                            decoration: BoxDecoration(
                                                              color: theme
                                                                  .primaryColor
                                                                  .withOpacity(
                                                                    0.1,
                                                                  ),
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    4,
                                                                  ),
                                                            ),
                                                            child: Text(
                                                              announcement['category'],
                                                              style: TextStyle(
                                                                fontSize: 10,
                                                                color:
                                                                    theme
                                                                        .primaryColor,
                                                              ),
                                                            ),
                                                          ),
                                                        if (announcement['category'] !=
                                                                null &&
                                                            announcement['location_name'] !=
                                                                null)
                                                          Gap(4.w),
                                                        if (announcement['location_name'] !=
                                                            null)
                                                          Container(
                                                            padding:
                                                                EdgeInsets.symmetric(
                                                                  horizontal: 6.w,
                                                                  vertical: 2.h,
                                                                ),
                                                            decoration: BoxDecoration(
                                                              color: Colors.orange
                                                                  .withOpacity(
                                                                    0.1,
                                                                  ),
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    4,
                                                                  ),
                                                            ),
                                                            child: Text(
                                                              announcement['location_name'],
                                                              style:
                                                                  const TextStyle(
                                                                    fontSize: 10,
                                                                    color:
                                                                        Colors
                                                                            .orange,
                                                                  ),
                                                            ),
                                                          ),
                                                        if (announcement['media'] !=
                                                            null)
                                                          Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                  left: 4.w,
                                                                ),
                                                            child: Icon(
                                                              Icons.image,
                                                              size: 12,
                                                              color:
                                                                  theme
                                                                      .primaryColor,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.delete,
                                                  color: Colors.red,
                                                  size: 20,
                                                ),
                                                onPressed:
                                                    () => controller
                                                        .removeAnnouncementFromList(
                                                          index,
                                                        ),
                                                tooltip: 'Remove from list',
                                                constraints: const BoxConstraints(
                                                  minWidth: 32,
                                                  minHeight: 32,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                  Gap(16.h),
                                  SizedBox(
                                    width: double.infinity,
                                    child: Obx(
                                      () => FilledButton.icon(
                                        onPressed:
                                            controller.isSubmittingMultiple.value
                                                ? null
                                                : _submitAllAnnouncements,
                                        icon:
                                            controller.isSubmittingMultiple.value
                                                ? const SizedBox(
                                                  width: 20,
                                                  height: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                      ),
                                                )
                                                : const Icon(IconlyLight.upload),
                                        label: Text(
                                          controller.isSubmittingMultiple.value
                                              ? 'Submitting...'
                                              : 'Submit All Announcement Items',
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                          : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
