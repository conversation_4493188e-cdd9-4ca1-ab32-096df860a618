import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/web.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/features/sermons/controllers/sermon_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../widgets/sermon_filter_widget.dart';

class ViewSermonsGrid extends StatefulWidget {
  const ViewSermonsGrid({super.key});

  @override
  State<ViewSermonsGrid> createState() => _ViewSermonsGridState();
}

class _ViewSermonsGridState extends State<ViewSermonsGrid> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final TextEditingController searchController = TextEditingController();
  String searchQuery = '';
  String statusFilter = 'All';
  setColumns() {
    // Initialize columns
    columns = [
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 260,
        enableRowChecked: true,
        renderer: (rendererContext) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                backgroundColor: Color(0xFFE0E2E7),
                radius: 15,
                child: Icon(
                  IconlyLight.document,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  "${rendererContext.row.cells['title']?.value ?? 'Untitled'}",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333843),
                  ),
                ),
              ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Username',
        field: 'username',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.row.cells['status']?.value;
          return Container(
            padding: const EdgeInsets.all(8),
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color:
                  status == "ACTIVE"
                      ? const Color(0xFFE7F4EE)
                      : const Color(0xFFFEEDEC),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              status.toString(),
              style: TextStyle(
                color: status == "ACTIVE" ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created',
        field: 'createdAt',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(IconlyLight.edit, size: 18),
                onPressed: () {
                  final sermonId = rendererContext.cell.value;
                  if (sermonId != null) {
                    final sermon = Get.find<SermonController>().sermons
                        .firstWhere((s) => s.id == sermonId);
                    context.push('/sermons/$sermonId/edit', extra: sermon);
                  }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                icon: const Icon(
                  IconlyLight.delete,
                  size: 18,
                  color: Colors.red,
                ),
                onPressed: () {
                  final sermonId = rendererContext.cell.value;
                  if (sermonId != null) {
                    _confirmDeleteSermon(context, sermonId);
                  }
                },
                tooltip: 'Delete',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  @override
  void initState() {
    setColumns();
    super.initState();
  }

  void _confirmDeleteSermon(BuildContext context, String sermonId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Sermon'),
            content: const Text('Are you sure you want to delete this sermon?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await Get.find<SermonController>()
                      .deleteSermon(sermonId);
                  if (success && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Sermon deleted successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    // Refresh the grid
                    Get.find<SermonController>().refreshSermons();
                  }
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sermons'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: buildSearchField(Get.find<SermonController>(), context),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt_outlined)
                      : Icon(Icons.filter_alt),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go(Routes.SERMON_CREATE);
        },
        child: const Icon(Icons.add),
      ),
      body: Center(
        child: Column(
          children: [
            Obx(
              () => showFilter.value ? const SermonFilterWidget() : SizedBox(),
            ),
            Expanded(
              child: SizedBox(
                width: double.infinity,
                child: Card(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  elevation: 4,
                  child: GetBuilder<SermonController>(
                    init: SermonController(),
                    initState: (state) async {
                      await state.controller.fetchSermons();
                    },
                    builder: (controll) {
                      if (controll.isLoading.value) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (controll.sermons.isNotEmpty) {
                        // sermon data to PlutoRows-----> one by one
                        if (kDebugMode) {
                          debugPrint(
                            "sermons Fetched: ${controll.sermons.length}",
                          );
                        }
                        rows =
                            controll.sermons.map((sermon) {
                              return PlutoRow(
                                cells: {
                                  'title': PlutoCell(
                                    value: sermon.title ?? 'Untitled',
                                  ),
                                  'description': PlutoCell(
                                    value:
                                        sermon.description ?? 'No description',
                                  ),
                                  'username': PlutoCell(
                                    value: sermon.username ?? 'N/A',
                                  ),
                                  'category': PlutoCell(
                                    value: sermon.category ?? 'N/A',
                                  ),
                                  'status': PlutoCell(
                                    value: sermon.status ?? 'N/A',
                                  ),
                                  'createdAt': PlutoCell(
                                    value:
                                        sermon.createdAt != null
                                            ? DateFormat(
                                              'dd MMM yyyy',
                                            ).format(sermon.createdAt!)
                                            : 'N/A',
                                  ),
                                  'actions': PlutoCell(value: sermon.id),
                                },
                              );
                            }).toList();

                        return PlutoGrid(
                          mode: PlutoGridMode.selectWithOneTap,
                          columns: columns,
                          rows: rows,

                          onLoaded: (PlutoGridOnLoadedEvent event) {},
                          onSelected: (event) {
                            try {
                              final sermon =
                                  controll.sermons[event.rowIdx ?? 0];
                              context.go(
                                Routes.SERMON_DETAIL.replaceAll(
                                  ':id',
                                  sermon.id ?? '',
                                ),
                                extra: sermon,
                              );
                            } catch (e) {
                              Logger().e(e);
                            }
                          },
                          onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
                            try {
                              final sermon = controll.sermons[event.rowIdx];
                              context.go(
                                Routes.SERMON_DETAIL.replaceAll(
                                  ':id',
                                  sermon.id ?? '',
                                ),
                                extra: sermon,
                              );
                            } catch (e) {
                              Logger().e(e);
                            }
                          },

                          configuration: PlutoGridConfiguration(
                            enableMoveDownAfterSelecting: true,
                            style: PlutoGridStyleConfig(
                              activatedColor: const Color.fromARGB(
                                255,
                                165,
                                205,
                                253,
                              ),
                              cellTextStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(255, 64, 64, 64),
                              ),
                              columnTextStyle: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blueGrey,
                              ),
                              rowHeight: 45,
                            ),
                            columnSize: const PlutoGridColumnSizeConfig(
                              autoSizeMode: PlutoAutoSizeMode.scale,
                              resizeMode: PlutoResizeMode.normal,
                              restoreAutoSizeAfterHideColumn: true,
                              restoreAutoSizeAfterFrozenColumn: true,
                              restoreAutoSizeAfterMoveColumn: true,
                            ),
                            columnFilter: const PlutoGridColumnFilterConfig(
                              filters: [...FilterHelper.defaultFilters],
                            ),
                          ),
                        );
                      } else if (controll.errorMessage.string.isNotEmpty) {
                        return Center(
                          child: Text(
                            controll.errorMessage.string,
                            style: TextStyle(color: Colors.red),
                          ),
                        );
                      } else {
                        // Show empty grid
                        return PlutoGrid(
                          mode: PlutoGridMode.selectWithOneTap,
                          columns: columns,
                          rows: [],
                          onLoaded: (PlutoGridOnLoadedEvent event) {},
                          configuration: PlutoGridConfiguration(
                            style: PlutoGridStyleConfig(
                              activatedColor: Color.fromARGB(
                                255,
                                165,
                                205,
                                253,
                              ),
                              cellTextStyle: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(255, 64, 64, 64),
                              ),
                              columnTextStyle: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blueGrey,
                              ),
                              rowHeight: 45,
                            ),
                            columnSize: const PlutoGridColumnSizeConfig(
                              autoSizeMode: PlutoAutoSizeMode.scale,
                              resizeMode: PlutoResizeMode.normal,
                              restoreAutoSizeAfterHideColumn: true,
                              restoreAutoSizeAfterFrozenColumn: true,
                              restoreAutoSizeAfterMoveColumn: true,
                            ),
                            scrollbar: const PlutoGridScrollbarConfig(
                              isAlwaysShown: false,
                            ),
                          ),
                          noRowsWidget: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  IconlyLight.document,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text('No sermons available'),
                              ],
                            ),
                          ),
                        );
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
