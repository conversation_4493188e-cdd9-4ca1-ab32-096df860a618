import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';

class EditCategoryFormWidget extends StatelessWidget {
  final String categoryId;
  final SubAccountCategory category;

  const EditCategoryFormWidget({
    super.key,
    required this.categoryId,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return GetBuilder<FinanceController>(
      init: Get.put(FinanceController()),
      builder: (controller) {
        return Form(
          key: form<PERSON><PERSON>,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      Gap(16.h),

                      // Title Field
                      CustomTextFormField(
                        controller: controller.titleController,
                        labelText: 'Category Title',
                        hintText: 'Enter category title (e.g., Donation)',
                        prefixIcon: const Icon(IconlyLight.category),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a category title';
                          }
                          if (value.length < 3) {
                            return 'Title must be at least 3 characters';
                          }
                          return null;
                        },
                        textCapitalization: TextCapitalization.words,
                      ),

                      Gap(16.h),

                      // Description Field
                      CustomTextFormField(
                        controller: controller.descriptionController,
                        labelText: 'Description',
                        hintText:
                            'Enter category description (e.g., Church donations)',
                        prefixIcon: const Icon(IconlyLight.document),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a description';
                          }
                          if (value.length < 10) {
                            return 'Description must be at least 10 characters';
                          }
                          return null;
                        },
                        textCapitalization: TextCapitalization.sentences,
                      ),

                      Gap(20.h),

                      // Category Info Section
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  IconlyLight.infoSquare,
                                  color: Colors.blue.shade700,
                                  size: 20,
                                ),
                                SizedBox(width: 12.w),
                                Text(
                                  'Category Details',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.blue.shade800,
                                  ),
                                ),
                              ],
                            ),
                            Gap(8.h),
                            _buildInfoRow('Category ID', category.id ?? 'N/A'),
                            _buildInfoRow(
                              'Type',
                              category.isGeneral == true
                                  ? 'General Category'
                                  : 'Organization Category',
                            ),
                            _buildInfoRow(
                              'Created',
                              category.createdAt != null
                                  ? '${category.createdAt!.day}/${category.createdAt!.month}/${category.createdAt!.year}'
                                  : 'N/A',
                            ),
                          ],
                        ),
                      ),

                      Gap(16.h),

                      // Warning Section
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              IconlyLight.dangerTriangle,
                              color: Colors.amber.shade700,
                              size: 20,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                'Changes to this category will affect all associated sub-accounts. Please review your changes carefully.',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.amber.shade800,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              Gap(24.h),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        controller.clearForm();
                        context.go(
                          Routes.VIEW_ACCOUNT_CATEGORY.replaceFirst(
                            ':id',
                            categoryId,
                          ),
                          extra: category,
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text('Cancel', style: TextStyle(fontSize: 16)),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    flex: 2,
                    child: Obx(
                      () => ElevatedButton(
                        onPressed:
                            controller.isSubmitting.value
                                ? null
                                : () async {
                                  if (formKey.currentState!.validate()) {
                                    final success = await _updateCategory(
                                      controller,
                                    );
                                    if (success && context.mounted) {
                                      context.go(
                                        Routes.VIEW_ACCOUNT_CATEGORY
                                            .replaceFirst(':id', categoryId),
                                      );
                                    }
                                  }
                                },
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child:
                            controller.isSubmitting.value
                                ? SizedBox(
                                  height: 20.h,
                                  width: 20.w,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : Text(
                                  'Update Category',
                                  style: TextStyle(fontSize: 16),
                                ),
                      ),
                    ),
                  ),
                ],
              ),

              Gap(16.h),

              // Error Message
              Obx(
                () =>
                    controller.message.value.isNotEmpty
                        ? Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.dangerTriangle,
                                color: Colors.red.shade700,
                                size: 20,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  controller.message.value,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.red.shade800,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade700,
            ),
          ),
          Text(value, style: TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  Future<bool> _updateCategory(FinanceController controller) async {
    return await controller.updateCategory(
      categoryId: categoryId,
      title: controller.titleController.text,
      description: controller.descriptionController.text,
    );
  }
}
