import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/inventory/controllers/inventory_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../models/inventory_record_model.dart';

class InventoryRecordsTableWidget extends StatefulWidget {
  const InventoryRecordsTableWidget({super.key});

  @override
  State<InventoryRecordsTableWidget> createState() =>
      _InventoryRecordsTableWidgetState();
}

class _InventoryRecordsTableWidgetState
    extends State<InventoryRecordsTableWidget> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  PlutoGridStateManager? stateManager;

  @override
  void initState() {
    super.initState();
    _setColumns();
  }

  void _setColumns() {
    columns = [
      PlutoColumn(
        title: 'Member/Person',
        field: 'member',
        type: PlutoColumnType.text(),
        width: 200,
        enableRowChecked: false,
        renderer: (rendererContext) {
          final record =
              rendererContext.row.cells['record']?.value
                  as InventoryRecordModel?;
          if (record == null) return const SizedBox();

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                backgroundColor: const Color(0xFFE0E2E7),
                radius: 15,
                backgroundImage:
                    record.member?.profileUrl != null
                        ? NetworkImage(record.member!.profileUrl!)
                        : null,
                child:
                    record.member?.profileUrl != null
                        ? null
                        : Icon(
                          record.isAnonymous == true
                              ? IconlyLight.profile
                              : IconlyLight.profile,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      record.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (record.phoneNumber != null &&
                        record.phoneNumber!.isNotEmpty)
                      Text(
                        record.phoneNumber!,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'Item',
        field: 'item',
        type: PlutoColumnType.text(),
        width: 180,
        renderer: (rendererContext) {
          final record =
              rendererContext.row.cells['record']?.value
                  as InventoryRecordModel?;
          if (record == null) return const SizedBox();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                record.itemName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if (record.inventoryItem?.category != null)
                Text(
                  record.inventoryItem!.category!,
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'Quantity',
        field: 'quantity',
        type: PlutoColumnType.number(),
        width: 80,
        renderer: (rendererContext) {
          final quantity = rendererContext.cell.value as int?;
          return Text(
            quantity?.toString() ?? '0',
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
            textAlign: TextAlign.center,
          );
        },
      ),
      PlutoColumn(
        title: 'Type',
        field: 'inventory_type',
        type: PlutoColumnType.text(),
        width: 120,
        renderer: (rendererContext) {
          final type = rendererContext.cell.value as String?;
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getTypeColor(type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getTypeColor(type).withOpacity(0.3)),
            ),
            child: Text(
              type ?? 'Unknown',
              style: TextStyle(
                color: _getTypeColor(type),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Condition',
        field: 'condition',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final condition = rendererContext.cell.value as String?;
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getConditionColor(condition).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getConditionColor(condition).withOpacity(0.3),
              ),
            ),
            child: Text(
              condition ?? 'Unknown',
              style: TextStyle(
                color: _getConditionColor(condition),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Value',
        field: 'estimated_value',
        type: PlutoColumnType.currency(),
        width: 100,
        renderer: (rendererContext) {
          final value = rendererContext.cell.value as double?;
          if (value == null || value == 0) return const Text('-');

          return Text(
            NumberFormat.currency(symbol: '\$').format(value),
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
          );
        },
      ),
      PlutoColumn(
        title: 'Received',
        field: 'received_at',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final receivedAt = rendererContext.cell.value as DateTime?;
          if (receivedAt == null) return const Text('Unknown');

          return Text(
            DateFormat('MMM dd, yyyy').format(receivedAt),
            style: const TextStyle(fontSize: 11),
          );
        },
      ),
      PlutoColumn(
        title: 'Expiry',
        field: 'expiry_date',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final record =
              rendererContext.row.cells['record']?.value
                  as InventoryRecordModel?;
          if (record?.expiryDate == null) return const Text('-');

          final isExpired = record!.isExpired;
          return Text(
            DateFormat('MMM dd, yyyy').format(record.expiryDate!),
            style: TextStyle(
              fontSize: 11,
              color: isExpired ? Colors.red : null,
              fontWeight: isExpired ? FontWeight.w600 : null,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        renderer: (rendererContext) {
          final record =
              rendererContext.row.cells['record']?.value
                  as InventoryRecordModel?;
          if (record == null) return const SizedBox();

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _viewRecord(record),
                icon: const Icon(IconlyLight.show, size: 16),
                tooltip: 'View Record',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                onPressed: () => _editRecord(record),
                icon: const Icon(IconlyLight.edit, size: 16),
                tooltip: 'Edit Record',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                onPressed: () => _deleteRecord(record),
                icon: const Icon(IconlyLight.delete, size: 16),
                tooltip: 'Delete Record',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  Color _getTypeColor(String? type) {
    switch (type?.toLowerCase()) {
      case 'donation':
        return Colors.green;
      case 'purchase':
        return Colors.blue;
      case 'transfer in':
        return Colors.teal;
      case 'transfer out':
        return Colors.orange;
      case 'lost':
      case 'damaged':
      case 'disposed':
        return Colors.red;
      case 'maintenance':
        return Colors.purple;
      case 'loan in':
        return Colors.indigo;
      case 'loan out':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  Color _getConditionColor(String? condition) {
    switch (condition?.toLowerCase()) {
      case 'new':
      case 'like new':
        return Colors.green;
      case 'good':
        return Colors.blue;
      case 'fair':
        return Colors.orange;
      case 'poor':
      case 'damaged':
      case 'needs repair':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _setRows() {
    final controller = Get.find<InventoryController>();
    rows =
        controller.inventoryRecords.map((record) {
          return PlutoRow(
            cells: {
              'record': PlutoCell(value: record),
              'member': PlutoCell(value: record.displayName),
              'item': PlutoCell(value: record.itemName),
              'quantity': PlutoCell(value: record.quantity ?? 0),
              'inventory_type': PlutoCell(
                value: record.inventoryType ?? 'Unknown',
              ),
              'condition': PlutoCell(value: record.condition ?? 'Unknown'),
              'estimated_value': PlutoCell(value: record.estimatedValue ?? 0.0),
              'received_at': PlutoCell(value: record.receivedAt),
              'expiry_date': PlutoCell(value: record.expiryDate),
              'actions': PlutoCell(value: ''),
            },
          );
        }).toList();
  }

  void _viewRecord(InventoryRecordModel record) {
    // Navigate to view record screen
    Get.snackbar(
      'View Record',
      'Viewing record for ${record.itemName}',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _editRecord(InventoryRecordModel record) {
    // Navigate to edit record screen
    Get.snackbar(
      'Edit Record',
      'Editing record for ${record.itemName}',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _deleteRecord(InventoryRecordModel record) {
    Get.defaultDialog(
      title: 'Delete Record',
      middleText: 'Are you sure you want to delete this inventory record?',
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      buttonColor: Colors.red,
      onConfirm: () async {
        Get.back();
        final controller = Get.find<InventoryController>();
        final success = await controller.deleteInventoryRecord(record.id ?? '');

        if (success) {
          ToastUtils.showSuccessToast('Record deleted successfully', null);
        } else {
          ToastUtils.showErrorToast('Failed to delete record', null);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InventoryController>(
      builder: (controller) {
        return Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    IconlyLight.dangerTriangle,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading inventory records',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    controller.errorMessage.value,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => controller.fetchInventoryRecords(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (controller.inventoryRecords.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    IconlyLight.document,
                    size: 64,
                    color: Colors.grey.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No inventory records found',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start by recording your first inventory transaction',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }

          _setRows();

          return Card(
            color: Theme.of(context).secondaryHeaderColor,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 4,
            child: PlutoGrid(
              mode: PlutoGridMode.selectWithOneTap,
              columns: columns,
              rows: rows,
              onLoaded: (PlutoGridOnLoadedEvent event) {
                stateManager = event.stateManager;
                event.stateManager.setShowColumnFilter(true);
                event.stateManager.setSelectingMode(PlutoGridSelectingMode.row);

                Future.microtask(() {
                  stateManager?.resetCurrentState();
                  stateManager?.notifyListeners();
                });

                if (kDebugMode) {
                  debugPrint("Inventory Records Grid loaded");
                }
              },
              onSelected: (event) {
                try {
                  final record = controller.inventoryRecords[event.rowIdx ?? 0];
                  _viewRecord(record);
                } catch (e) {
                  Logger().e(e);
                }
              },
              configuration: PlutoGridConfiguration(
                enableMoveDownAfterSelecting: true,
                style: PlutoGridStyleConfig(
                  activatedColor: const Color.fromARGB(255, 165, 205, 253),
                  cellTextStyle: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color.fromARGB(255, 64, 64, 64),
                  ),
                  columnTextStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blueGrey,
                  ),
                  rowHeight: 50,
                ),
                columnSize: const PlutoGridColumnSizeConfig(
                  autoSizeMode: PlutoAutoSizeMode.scale,
                  resizeMode: PlutoResizeMode.normal,
                ),
                columnFilter: const PlutoGridColumnFilterConfig(
                  filters: [...FilterHelper.defaultFilters],
                ),
              ),
            ),
          );
        });
      },
    );
  }
}
