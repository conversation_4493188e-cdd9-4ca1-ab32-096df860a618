import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/widgets/custom_dropdown.dart';
import '../../../../core/app/widgets/custom_textformfield.dart';
import '../../../media_upload/media_upload.dart';
import '../../controllers/record_inventory_controller.dart';

class RecordInventoryScreen extends StatelessWidget {
  const RecordInventoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final RecordInventoryController controller =
        Get.find<RecordInventoryController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Record Inventory',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(Routes.INVENTORY),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshData(),
            tooltip: 'Refresh Data',
          ),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Record Inventory Movement',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(8.h),
              Text(
                'Track inventory items coming in or going out of your organization',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              Gap(24.h),

              // Inventory Item Selection
              Text(
                'Item Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(16.h),

              // Inventory Item Dropdown
              Obx(() {
                debugPrint(
                  'Building inventory items dropdown: loading=${controller.isLoadingItems.value}, items=${controller.inventoryItems.length}',
                );

                if (controller.isLoadingItems.value) {
                  return Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      border: Border.all(color: colorScheme.outline),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 20.w,
                          height: 20.h,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              colorScheme.primary,
                            ),
                          ),
                        ),
                        Gap(12.w),
                        Text(
                          'Loading inventory items...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                if (controller.inventoryItems.isEmpty) {
                  return Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      border: Border.all(color: colorScheme.error),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          IconlyLight.dangerTriangle,
                          color: colorScheme.error,
                        ),
                        Gap(12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'No inventory items found',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.error,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'Please add inventory items first or refresh',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.error.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return CustomDropdown<String>(
                  labelText: 'Inventory Item *',
                  value:
                      controller.selectedInventoryItemId.value.isEmpty
                          ? null
                          : controller.selectedInventoryItemId.value,
                  hintText: 'Select an inventory item',
                  prefixIcon: Icon(IconlyLight.bag, color: colorScheme.primary),
                  items:
                      controller.inventoryItems.map((item) {
                        return DropdownMenuItem<String>(
                          value: item.id,
                          child: Text(item.title ?? 'Unknown'),
                        );
                      }).toList(),
                  onChanged: controller.setSelectedInventoryItem,
                  validator: controller.validateInventoryItem,
                );
              }),
              Gap(16.h),

              // Quantity field
              CustomTextFormField(
                controller: controller.quantityController,
                labelText: 'Quantity *',
                hintText: 'Enter quantity',
                prefixIcon: Icon(IconlyLight.chart, color: colorScheme.primary),
                keyboardType: TextInputType.number,
                validator: controller.validateQuantity,
              ),
              Gap(16.h),

              // Condition dropdown
              Obx(() {
                debugPrint(
                  'Building condition dropdown with ${controller.conditions.length} items',
                );
                return controller.conditions.isEmpty
                    ? Container(
                      padding: EdgeInsets.all(16.r),
                      decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.outline),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Row(
                        children: [
                          Icon(IconlyLight.star, color: colorScheme.primary),
                          Gap(12.w),
                          Text(
                            'Loading conditions...',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    )
                    : CustomDropdown<String>(
                      labelText: 'Condition *',
                      value:
                          controller.selectedCondition.value.isEmpty
                              ? null
                              : controller.selectedCondition.value,
                      hintText: 'Select condition',
                      prefixIcon: Icon(
                        IconlyLight.star,
                        color: colorScheme.primary,
                      ),
                      items:
                          controller.conditions.map((condition) {
                            return DropdownMenuItem<String>(
                              value: condition,
                              child: Text(condition),
                            );
                          }).toList(),
                      onChanged: controller.setSelectedCondition,
                      validator: controller.validateCondition,
                    );
              }),
              Gap(16.h),

              // Inventory Type dropdown
              Obx(() {
                debugPrint(
                  'Building inventory type dropdown with ${controller.inventoryTypes.length} items',
                );
                return controller.inventoryTypes.isEmpty
                    ? Container(
                      padding: EdgeInsets.all(16.r),
                      decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.outline),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            IconlyLight.category,
                            color: colorScheme.primary,
                          ),
                          Gap(12.w),
                          Text(
                            'Loading inventory types...',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    )
                    : CustomDropdown<String>(
                      labelText: 'Inventory Type *',
                      value:
                          controller.selectedInventoryType.value.isEmpty
                              ? null
                              : controller.selectedInventoryType.value,
                      hintText: 'Select inventory type',
                      prefixIcon: Icon(
                        IconlyLight.category,
                        color: colorScheme.primary,
                      ),
                      items:
                          controller.inventoryTypes.map((type) {
                            return DropdownMenuItem<String>(
                              value: type,
                              child: Text(type),
                            );
                          }).toList(),
                      onChanged: controller.setSelectedInventoryType,
                      validator: controller.validateInventoryType,
                    );
              }),
              Gap(24.h),

              // Dates Section
              Text(
                'Date Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(16.h),

              // Received At Date
              Obx(
                () => InkWell(
                  onTap: () => _selectReceivedDate(context, controller),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 16.h,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: colorScheme.outline),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        Icon(IconlyLight.calendar, color: colorScheme.primary),
                        Gap(12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Received Date *',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.primary,
                                ),
                              ),
                              Gap(4.h),
                              Text(
                                controller.receivedAt.value != null
                                    ? DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(controller.receivedAt.value!)
                                    : 'Select received date',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap(16.h),

              // Optional Dates Row
              Row(
                children: [
                  // Expiry Date
                  Expanded(
                    child: Obx(
                      () => InkWell(
                        onTap: () => _selectExpiryDate(context, controller),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 16.h,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: colorScheme.outline),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.calendar,
                                color: colorScheme.primary,
                              ),
                              Gap(8.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Expiry Date',
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(
                                            color: colorScheme.primary,
                                          ),
                                    ),
                                    Gap(4.h),
                                    Text(
                                      controller.expiryDate.value != null
                                          ? DateFormat(
                                            'MMM dd, yyyy',
                                          ).format(controller.expiryDate.value!)
                                          : 'Optional',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Gap(12.w),
                  // Purchase Date
                  Expanded(
                    child: Obx(
                      () => InkWell(
                        onTap: () => _selectPurchaseDate(context, controller),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 16.h,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: colorScheme.outline),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.calendar,
                                color: colorScheme.primary,
                              ),
                              Gap(8.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Purchase Date',
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(
                                            color: colorScheme.primary,
                                          ),
                                    ),
                                    Gap(4.h),
                                    Text(
                                      controller.purchaseDate.value != null
                                          ? DateFormat('MMM dd, yyyy').format(
                                            controller.purchaseDate.value!,
                                          )
                                          : 'Optional',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Gap(24.h),

              // Additional Information Section
              Text(
                'Additional Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(16.h),

              // Batch Number
              CustomTextFormField(
                controller: controller.batchNoController,
                labelText: 'Batch Number',
                hintText: 'Enter batch number (optional)',
                prefixIcon: Icon(
                  IconlyLight.document,
                  color: colorScheme.primary,
                ),
              ),
              Gap(16.h),

              // Financial Information Row
              Row(
                children: [
                  // Estimated Value
                  Expanded(
                    child: CustomTextFormField(
                      controller: controller.estimatedValueController,
                      labelText: 'Estimated Value',
                      hintText: 'Enter value',
                      prefixIcon: Icon(
                        IconlyLight.wallet,
                        color: colorScheme.primary,
                      ),
                      keyboardType: TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: controller.validateEstimatedValue,
                    ),
                  ),
                  Gap(12.w),
                  // Cost
                  Expanded(
                    child: CustomTextFormField(
                      controller: controller.costController,
                      labelText: 'Cost',
                      hintText: 'Enter cost',
                      prefixIcon: Icon(
                        IconlyLight.wallet,
                        color: colorScheme.primary,
                      ),
                      keyboardType: TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: controller.validateCost,
                    ),
                  ),
                ],
              ),
              Gap(16.h),

              // Warranty Expiry Date
              Obx(
                () => InkWell(
                  onTap: () => _selectWarrantyDate(context, controller),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 16.h,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: colorScheme.outline),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          IconlyLight.shieldDone,
                          color: colorScheme.primary,
                        ),
                        Gap(12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Warranty Expiry',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.primary,
                                ),
                              ),
                              Gap(4.h),
                              Text(
                                controller.warrantyExpiry.value != null
                                    ? DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(controller.warrantyExpiry.value!)
                                    : 'Select warranty expiry (optional)',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap(16.h),

              // Notes
              CustomTextFormField(
                controller: controller.notesController,
                labelText: 'Notes',
                hintText: 'Enter any additional notes (optional)',
                prefixIcon: Icon(
                  IconlyLight.document,
                  color: colorScheme.primary,
                ),
                maxLines: 3,
                keyboardType: TextInputType.multiline,
              ),
              Gap(24.h),

              // Member/Anonymous Section
              Text(
                'Member Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(16.h),

              // Anonymous Toggle
              Obx(
                () => Card(
                  elevation: 0,
                  color: colorScheme.surfaceVariant.withOpacity(0.3),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    side: BorderSide(
                      color: colorScheme.outline.withOpacity(0.5),
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              IconlyLight.profile,
                              color: colorScheme.primary,
                            ),
                            Gap(8.w),
                            Expanded(
                              child: Text(
                                'Record as Anonymous',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Switch(
                              value: controller.isAnonymous.value,
                              onChanged: controller.setIsAnonymous,
                              activeColor: colorScheme.primary,
                            ),
                          ],
                        ),
                        Gap(8.h),
                        Text(
                          controller.isAnonymous.value
                              ? 'Recording as anonymous - fill in contact details below'
                              : 'Recording for a member - select member below',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap(16.h),

              // Member Selection or Anonymous Fields
              Obx(
                () =>
                    controller.isAnonymous.value
                        ? _buildAnonymousFields(controller, colorScheme, theme)
                        : _buildMemberSelection(
                          context,
                          controller,
                          colorScheme,
                          theme,
                        ),
              ),
              Gap(24.h),

              // Media Upload Section
              Card(
                elevation: 0,
                color: colorScheme.surfaceVariant.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  side: BorderSide(color: colorScheme.outline.withOpacity(0.5)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(IconlyLight.image, color: colorScheme.primary),
                          Gap(8.w),
                          Text(
                            'Media (Optional)',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      Gap(8.h),
                      Text(
                        'Add photos or documents related to this inventory record',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      Gap(16.h),
                      MediaUploadWidget(
                        category: 'INVENTORY_RECORD',
                        multipleSelect: true,
                        onMediaSelected: controller.setMediaItems,
                        width: double.infinity,
                        height: 120.h,
                      ),
                      Gap(12.h),
                      Obx(
                        () =>
                            controller.mediaItems.isEmpty
                                ? Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(16.r),
                                    child: Text(
                                      'No media items added yet',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            color: colorScheme.onSurface
                                                .withOpacity(0.6),
                                          ),
                                    ),
                                  ),
                                )
                                : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: controller.mediaItems.length,
                                  itemBuilder: (context, index) {
                                    final item = controller.mediaItems[index];
                                    return ListTile(
                                      contentPadding: EdgeInsets.zero,
                                      leading: ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          4.r,
                                        ),
                                        child: Image.network(
                                          item.mediaUrl ?? '',
                                          width: 40.w,
                                          height: 40.h,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                                    width: 40.w,
                                                    height: 40.h,
                                                    color:
                                                        colorScheme
                                                            .surfaceVariant,
                                                    child: Icon(
                                                      Icons.error,
                                                      size: 20.r,
                                                      color: colorScheme.error,
                                                    ),
                                                  ),
                                        ),
                                      ),
                                      title: Text(
                                        item.title ?? 'Media Item',
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                      subtitle: Text(
                                        item.mediaUrl ?? '',
                                        overflow: TextOverflow.ellipsis,
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: colorScheme.onSurface
                                                  .withOpacity(0.6),
                                            ),
                                      ),
                                      trailing: IconButton(
                                        icon: Icon(
                                          Icons.delete,
                                          color: colorScheme.error,
                                          size: 20,
                                        ),
                                        onPressed:
                                            () => controller.removeMediaItem(
                                              index,
                                            ),
                                        tooltip: 'Remove',
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(32.h),

              // Submit Button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  height: 48.h,
                  child: ElevatedButton(
                    onPressed:
                        controller.isLoading.value
                            ? null
                            : () => controller.submitForm(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child:
                        controller.isLoading.value
                            ? SizedBox(
                              height: 20.h,
                              width: 20.w,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : Text(
                              'Record Inventory',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),
              ),
              Gap(16.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnonymousFields(
    RecordInventoryController controller,
    ColorScheme colorScheme,
    ThemeData theme,
  ) {
    return Column(
      children: [
        // Full Names
        CustomTextFormField(
          controller: controller.fullNamesController,
          labelText: 'Full Names *',
          hintText: 'Enter full names',
          prefixIcon: Icon(IconlyLight.profile, color: colorScheme.primary),
          validator: (value) {
            if (controller.isAnonymous.value &&
                (value == null || value.trim().isEmpty)) {
              return 'Please enter full names';
            }
            return null;
          },
        ),
        Gap(16.h),
        // Email and Phone Row
        Row(
          children: [
            Expanded(
              child: CustomTextFormField(
                controller: controller.emailController,
                labelText: 'Email',
                hintText: 'Enter email',
                prefixIcon: Icon(
                  IconlyLight.message,
                  color: colorScheme.primary,
                ),
                keyboardType: TextInputType.emailAddress,
              ),
            ),
            Gap(12.w),
            Expanded(
              child: CustomTextFormField(
                controller: controller.phoneNumberController,
                labelText: 'Phone Number',
                hintText: 'Enter phone',
                prefixIcon: Icon(IconlyLight.call, color: colorScheme.primary),
                keyboardType: TextInputType.phone,
              ),
            ),
          ],
        ),
        Gap(16.h),
        // County and City Row
        Row(
          children: [
            Expanded(
              child: CustomTextFormField(
                controller: controller.countyController,
                labelText: 'County',
                hintText: 'Enter county',
                prefixIcon: Icon(
                  IconlyLight.location,
                  color: colorScheme.primary,
                ),
              ),
            ),
            Gap(12.w),
            Expanded(
              child: CustomTextFormField(
                controller: controller.cityController,
                labelText: 'City',
                hintText: 'Enter city',
                prefixIcon: Icon(
                  IconlyLight.location,
                  color: colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
        Gap(16.h),
        // Address
        CustomTextFormField(
          controller: controller.addressController,
          labelText: 'Address',
          hintText: 'Enter address',
          prefixIcon: Icon(IconlyLight.location, color: colorScheme.primary),
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildMemberSelection(
    BuildContext context,
    RecordInventoryController controller,
    ColorScheme colorScheme,
    ThemeData theme,
  ) {
    return Obx(
      () => InkWell(
        onTap: () => controller.selectMember(context),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            border: Border.all(color: colorScheme.outline),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Icon(IconlyLight.profile, color: colorScheme.primary),
              Gap(12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Member *',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    Gap(4.h),
                    Text(
                      controller.selectedMember.value != null
                          ? '${controller.selectedMember.value!.firstName} ${controller.selectedMember.value!.secondName}'
                          : 'Tap to select a member',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color:
                            controller.selectedMember.value != null
                                ? colorScheme.onSurface
                                : colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.r,
                color: colorScheme.onSurface.withOpacity(0.6),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectReceivedDate(
    BuildContext context,
    RecordInventoryController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.receivedAt.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      controller.setReceivedAt(picked);
    }
  }

  Future<void> _selectExpiryDate(
    BuildContext context,
    RecordInventoryController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          controller.expiryDate.value ??
          DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      controller.setExpiryDate(picked);
    }
  }

  Future<void> _selectPurchaseDate(
    BuildContext context,
    RecordInventoryController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.purchaseDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.setPurchaseDate(picked);
    }
  }

  Future<void> _selectWarrantyDate(
    BuildContext context,
    RecordInventoryController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          controller.warrantyExpiry.value ??
          DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      controller.setWarrantyExpiry(picked);
    }
  }
}
