import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../core/app/constants/routes.dart';
import '../../controllers/hymn_controller.dart';
import '../../../../data/models/hymn_model.dart';

class HymnsScreen extends GetView<HymnController> {
  const HymnsScreen({super.key});

  ScrollController _getScrollController(HymnController controller) {
    final ScrollController scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        // We've hit the bottom, load more data
        if (!controller.isLoadingMore.value && controller.hasMoreData.value) {
          controller.currentPage.value++;
          controller.fetchHymns(loadMore: true);
        }
      }
    });
    return scrollController;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Hymns',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(IconlyLight.filter),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(IconlyLight.bookmark),
            onPressed: () => context.go(Routes.DOWNLOADED_HYMNS),
          ),
          IconButton(
            icon: const Icon(IconlyLight.upload),
            onPressed: () => context.go(Routes.UPLOAD_HYMN),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.hymns.isEmpty) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(height: 24),
                Skeletonizer(child: _buildSectionTitle('Hymns')),
                const SizedBox(height: 16),
                ListView.builder(
                  shrinkWrap: true,
                  itemCount: 3,
                  itemBuilder: (context, index) {
                    return Skeletonizer(
                      child: Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        elevation: 0,
                        color: colorScheme.surface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              Container(
                                height: 80,
                                width: 80,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: colorScheme.primaryContainer,
                                ),
                                child: const Center(
                                  child: Icon(
                                    IconlyBold.play,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Hymn Title',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Artist • Album',
                                      style: TextStyle(
                                        color: colorScheme.onSurface
                                            .withOpacity(0.6),
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        }

        if (controller.hymns.isEmpty && !controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.music_note, size: 48),
                const SizedBox(height: 16),
                Text('No hymns found', style: theme.textTheme.titleMedium),
                if (controller.searchQuery.value.isNotEmpty ||
                    controller.startDate.value != null ||
                    controller.endDate.value != null)
                  TextButton(
                    onPressed: () => controller.clearFilters(),
                    child: const Text('Clear filters'),
                  ),
              ],
            ),
          );
        }
        return RefreshIndicator(
          onRefresh: () => controller.refreshHymns(),
          child: SingleChildScrollView(
            controller: _getScrollController(controller),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (controller.hymns.isNotEmpty) _buildFeaturedHymn(context),
                const SizedBox(height: 24),
                _buildSectionTitle('Hymns'),
                if (controller.searchQuery.value.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Search results for: "${controller.searchQuery.value}"',
                          ),
                        ),
                        TextButton(
                          onPressed: () => controller.clearFilters(),
                          child: const Text('Clear'),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 16),
                _buildHymnsList(context),
                const SizedBox(height: 16),
                if (controller.isLoading.value &&
                    controller.hymns.isNotEmpty) ...[
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: 2,
                    itemBuilder: (context, index) {
                      return Skeletonizer(
                        child: Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 0,
                          color: colorScheme.surface,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Row(
                              children: [
                                Container(
                                  height: 80,
                                  width: 80,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: colorScheme.primaryContainer,
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.music_note,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Hymn Title',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Artist • Album',
                                        style: TextStyle(
                                          color: colorScheme.onSurface
                                              .withOpacity(0.6),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
                if (controller.isLoadingMore.value)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            colorScheme.primary,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      }),

      // bottomNavigationBar: const BottomNavBar(currentIndex: 2),
    );
  }

  Widget _buildFeaturedHymn(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final featuredHymn = controller.hymns.first;

    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
        image: const DecorationImage(
          image: AssetImage('assets/images/hymn_bg.jpg'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(Colors.black38, BlendMode.darken),
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 5,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'FEATURED',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  featuredHymn.title ?? 'Untitled Hymn',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '${featuredHymn.artist} • ${featuredHymn.album}',
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 20,
            right: 20,
            child: CircleAvatar(
              backgroundColor: colorScheme.primary,
              radius: 24,
              child: IconButton(
                icon: const Icon(IconlyBold.play, color: Colors.white),
                onPressed: () => _navigateToHymnDetail(context, featuredHymn),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildHymnsList(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final hymns = controller.hymns;

    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: List.generate(hymns.length, (index) {
        final hymn = hymns[index];
        // Skip the first hymn if it's already featured
        if (index == 0 && hymns.length > 1) {
          return const SizedBox.shrink();
        }

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 0,
          color: colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _navigateToHymnDetail(context, hymn),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: colorScheme.primaryContainer,
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.music_note,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          hymn.title ?? 'Untitled Hymn',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${hymn.artist} • ${hymn.album}',
                          style: TextStyle(
                            color: colorScheme.onSurface.withOpacity(0.6),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              IconlyLight.category,
                              size: 14,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              hymn.categories.isNotEmpty == true &&
                                      hymn.categories.first.name != null
                                  ? hymn.categories.first.name!
                                  : 'Uncategorized',
                              style: TextStyle(
                                color: colorScheme.primary,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  void _navigateToHymnDetail(BuildContext context, HymnModel hymn) {
    context.go('${Routes.HYMN_DETAIL}/${hymn.id}', extra: hymn);
  }

  void _showSearchDialog(BuildContext context) {
    final searchController = TextEditingController(
      text: controller.searchQuery.value,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Hymns'),
            content: CustomTextField(
              controller: searchController,
              hintText: 'Enter hymn title, artist, or album',

              onSubmitted: (value) {
                controller.setSearchQuery(value);
                Navigator.pop(context);
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  controller.setSearchQuery(searchController.text);
                  Navigator.pop(context);
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    DateTime? startDate = controller.startDate.value;
    DateTime? endDate = controller.endDate.value;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filter Hymns'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(IconlyLight.calendar),
                  title: const Text('Start Date'),
                  subtitle: Text(
                    startDate != null
                        ? DateFormat('yyyy-MM-dd').format(startDate!)
                        : 'Not set',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (date != null) {
                      startDate = date;
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(IconlyLight.calendar),
                  title: const Text('End Date'),
                  subtitle: Text(
                    endDate != null
                        ? DateFormat('yyyy-MM-dd').format(endDate!)
                        : 'Not set',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (date != null) {
                      endDate = date;
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  controller.setDateFilters(startDate, endDate);
                  Navigator.of(context).pop();
                },
                child: const Text('Apply'),
              ),
              TextButton(
                onPressed: () {
                  controller.clearFilters();
                  Navigator.of(context).pop();
                },
                child: const Text('Clear Filters'),
              ),
            ],
          ),
    );
  }
}
