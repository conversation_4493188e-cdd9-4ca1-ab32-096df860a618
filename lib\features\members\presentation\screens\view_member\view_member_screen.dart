import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/show_member_groups.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/show_member_relationships.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/show_member_transactions.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/widgets/section_header.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:onechurch/core/app/utils/screen_breakpoints.dart';
import 'package:onechurch/core/app/widgets/welcome_widget.dart';
import 'package:onechurch/features/group/controllers/group_controller.dart';
import 'package:onechurch/features/members/presentation/widgets/info_cards.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../../../core/app/utils/show_toast.dart';
import '../../../../../data/models/member_model.dart';
import '../../../controllers/member_controller.dart';

class ViewMemberScreen extends StatefulWidget {
  final String memberId;
  const ViewMemberScreen({super.key, required this.memberId});

  @override
  State<ViewMemberScreen> createState() => _ViewMemberScreenState();
}

class _ViewMemberScreenState extends State<ViewMemberScreen> {
  final controller = Get.find<MemberController>();
  final groupController = Get.find<GroupController>();
  final isLoading = true.obs;
  final errorMessage = ''.obs;
  final member = Rx<MemberModel?>(null);

  @override
  void initState() {
    super.initState();
    _loadMember();
  }

  Future<void> _loadMember() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // Find the member in the existing list first for quick display
      final existingMember = controller.members.firstWhereOrNull(
        (m) => m.id == widget.memberId,
      );

      if (existingMember != null) {
        member.value = existingMember;
      }

      // Then fetch the latest data from the API
      final result = await controller.getMemberById(widget.memberId);

      if (result != null) {
        member.value = result;
      } else {
        errorMessage.value = 'Member not found';
        ToastUtils.showErrorToast(errorMessage.value, null);
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Determine screen size category
    final isMobile = screenWidth < ScreenBreakpoints.mobile;
    final isTablet =
        screenWidth >= ScreenBreakpoints.mobile &&
        screenWidth < ScreenBreakpoints.tablet;
    final isDesktopSmall =
        screenWidth >= ScreenBreakpoints.tablet &&
        screenWidth < ScreenBreakpoints.desktopLarge;
    final isDesktopLarge = screenWidth >= ScreenBreakpoints.desktopLarge;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Member Details'),
        elevation: isDesktopLarge ? 0 : null,
        scrolledUnderElevation: isDesktopLarge ? 0 : 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.connect_without_contact_sharp),
            onPressed: () {
              context.go(
                Routes.VIEW_MEMBER_RELATIONS.replaceFirst(
                  ':id',
                  widget.memberId,
                ),
              );
            },
            tooltip: 'View Member Relationships',
          ),

          IconButton(
            icon: const Icon(IconlyLight.edit),
            onPressed: () {
              // Navigate to edit member screen
              context.go(
                Routes.EDIT_MEMBER.replaceFirst(':id', widget.memberId),
              );
            },
            tooltip: 'Edit Member',
          ),

          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMember,
            tooltip: 'Refresh Member Data',
          ),
        ],
      ),
      body: Obx(() {
        // Loading state
        if (isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 60.r,
                  height: 60.r,
                  child: CircularProgressIndicator(
                    strokeWidth: 3.r,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                Gap(24.h),
                Text(
                  'Loading Member Details...',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          );
        }

        // Error state
        if (errorMessage.isNotEmpty && member.value == null) {
          final screenWidth = MediaQuery.of(context).size.width;
          final isMobile = screenWidth < ScreenBreakpoints.mobile;
          final isDesktop = screenWidth >= ScreenBreakpoints.tablet;

          return Center(
            child: Container(
              width: isDesktop ? 500.w : (isMobile ? double.infinity : 400.w),
              padding: EdgeInsets.all(
                isDesktop ? 32.r : (isMobile ? 16.r : 24.r),
              ),
              margin: EdgeInsets.all(isMobile ? 16.r : 0),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: theme.colorScheme.error.withOpacity(0.3),
                  width: 1.r,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    IconlyLight.dangerCircle,
                    size: isDesktop ? 64 : 48,
                    color: theme.colorScheme.error,
                  ),
                  Gap(16.h),
                  Text(
                    'Error Loading Member',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.error,
                    ),
                  ),
                  Gap(8.h),
                  Text(
                    errorMessage.value,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  Gap(24.h),
                  SizedBox(
                    width:
                        isDesktop
                            ? 200.w
                            : (isMobile ? double.infinity : 180.w),
                    child: FilledButton.icon(
                      onPressed: _loadMember,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                    ),
                  ),
                  if (!isMobile) Gap(8.h),
                  if (!isMobile)
                    TextButton(
                      onPressed: () {
                        context.go(Routes.MEMBERS);
                      },
                      child: const Text('Back to Members List'),
                    ),
                ],
              ),
            ),
          );
        }

        // Member not found state
        final m = member.value;
        if (m == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyLight.search,
                  size: 64,
                  color: theme.colorScheme.primary.withOpacity(0.5),
                ),
                Gap(16.h),
                Text(
                  'Member Not Found',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Gap(8.h),
                Text(
                  'The member you are looking for could not be found.',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Gap(24.h),
                FilledButton.icon(
                  onPressed: () {
                    context.go(Routes.MEMBERS);
                  },
                  icon: const Icon(IconlyLight.arrowLeft),
                  label: const Text('Back to Members'),
                ),
              ],
            ),
          );
        }

        // Build the profile header section
        final profileHeader = ProfileHeader(m: m);

        // Build the information sections
        final contactInfoSection = InfoSection(
          title: 'Contact Information',
          infoCards: [
            InfoCards(
              title: 'Phone Number',
              value: m.phoneNumber ?? 'N/A',
              icon: IconlyLight.call,
              action:
                  m.phoneNumber != null && m.phoneNumber!.isNotEmpty
                      ? () async {
                        final phoneUri = Uri.parse('tel:${m.phoneNumber}');
                        try {
                          // Launch phone dialer
                          if (!await launchUrl(phoneUri)) {
                            ToastUtils.showErrorToast(
                              'Could not launch phone dialer',
                              null,
                            );
                          }
                        } catch (e) {
                          ToastUtils.showErrorToast(
                            'Error: ${e.toString()}',
                            null,
                          );
                        }
                      }
                      : null,
            ),
            if (m.secondaryNumber != null && m.secondaryNumber!.isNotEmpty)
              InfoCards(
                title: 'Secondary Phone',
                value: m.secondaryNumber!,
                icon: IconlyLight.call,
                action: () async {
                  final phoneUri = Uri.parse('tel:${m.secondaryNumber}');
                  try {
                    // Launch phone dialer
                    if (!await launchUrl(phoneUri)) {
                      ToastUtils.showErrorToast(
                        'Could not launch phone dialer',
                        null,
                      );
                    }
                  } catch (e) {
                    ToastUtils.showErrorToast('Error: ${e.toString()}', null);
                  }
                },
              ),
            if (m.email != null && m.email!.isNotEmpty)
              InfoCards(
                title: 'Email',
                value: m.email!,
                icon: IconlyLight.message,
                action: () async {
                  final emailUri = Uri.parse('mailto:${m.email}');
                  try {
                    // Launch email client
                    if (!await launchUrl(emailUri)) {
                      ToastUtils.showErrorToast(
                        'Could not launch email client',
                        null,
                      );
                    }
                  } catch (e) {
                    ToastUtils.showErrorToast('Error: ${e.toString()}', null);
                  }
                },
              ),
            if (m.address != null && m.address!.isNotEmpty)
              InfoCards(
                title: 'Address',
                value: m.address!,
                icon: IconlyLight.location,
                action: () async {
                  final mapUri = Uri.parse(
                    'https://maps.google.com/?q=${Uri.encodeComponent(m.address!)}',
                  );
                  try {
                    // Launch maps
                    if (!await launchUrl(
                      mapUri,
                      mode: LaunchMode.externalApplication,
                    )) {
                      ToastUtils.showErrorToast('Could not launch maps', null);
                    }
                  } catch (e) {
                    ToastUtils.showErrorToast('Error: ${e.toString()}', null);
                  }
                },
              ),
          ],
        );

        final personalInfoSection = InfoSection(
          title: 'Personal Information',
          infoCards: [
            InfoCards(
              title: 'ID Number',
              value: m.idNumber ?? 'N/A',
              icon: IconlyLight.ticket,
            ),
            InfoCards(
              title: 'Date of Birth',
              value: formatDate(m.dob),
              icon: IconlyLight.calendar,
            ),
            InfoCards(
              title: 'Join Date',
              value: formatDate(m.joinDate),
              icon: IconlyLight.timeCircle,
            ),
            InfoCards(
              title: 'Baptism Date',
              value:
                  m.baptismDate != null
                      ? formatDate(DateTime.parse(m.baptismDate.toString()))
                      : 'N/A',
              icon: IconlyLight.paper,
            ),
          ],
        );

        final additionalInfoSection = InfoSection(
          title: 'Additional Information',
          infoCards: [
            InfoCards(
              title: 'Gender',
              value: m.gender ?? 'N/A',
              icon: IconlyLight.profile,
            ),
            InfoCards(
              title: 'Marital Status',
              value: m.maritalStatus ?? 'N/A',
              icon: IconlyLight.heart,
            ),
            InfoCards(
              title: 'Occupation',
              value: m.occupation ?? 'N/A',
              icon: IconlyLight.work,
            ),
            InfoCards(
              title: 'Education Level',
              value: m.educationLevel ?? 'N/A',
              icon: IconlyLight.document,
            ),
          ],
        );

        final membershipInfoSection = InfoSection(
          title: 'Membership Information',
          infoCards: [
            InfoCards(
              title: 'Verification Status',
              value: m.verificationStatus ?? 'N/A',
              icon: IconlyLight.shieldDone,
            ),
            InfoCards(
              title: 'Member Category',
              value:
                  m.memberCategory != null
                      ? m.memberCategory.toString()
                      : 'N/A',
              icon: IconlyLight.category,
            ),
          ],
        );

        final systemInfoSection = InfoSection(
          title: 'System Information',
          infoCards: [
            InfoCards(
              title: 'Created At',
              value: formatDate(m.createdAt),
              icon: IconlyLight.timeCircle,
            ),
            InfoCards(
              title: 'Last Updated',
              value: formatDate(m.updatedAt),
              icon: IconlyLight.editSquare,
            ),
          ],
        );

        // Mobile layout (single column)
        if (isMobile) {
          return DefaultTabController(
            length: 5,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile and primary info sections
                  profileHeader,
                  Gap(24.h),
                  // Primary info sections
                  contactInfoSection,
                  Gap(24.h),
                  personalInfoSection,
                  Gap(32.h),

                  // Tab bar
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        width: 1.r,
                      ),
                    ),
                    child: TabBar(
                      tabs: [
                        Tab(text: 'Analytics'),
                        Tab(text: 'Additional Info'),
                        Tab(text: 'Groups'),
                        Tab(text: 'Transactions'),
                        Tab(text: 'Relationships'),
                      ],
                      labelColor: theme.colorScheme.onPrimary,
                      unselectedLabelColor: theme.colorScheme.onSurface
                          .withOpacity(0.7),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicatorWeight: 3,
                      indicator: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.all(4.r),
                      labelPadding: EdgeInsets.symmetric(horizontal: 8.r),
                    ),
                  ),

                  // Tab content
                  SizedBox(
                    height: 600.h, // Fixed height for tab content
                    child: TabBarView(
                      children: [
                        // Tab 1: Analytics
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeader(title: 'Member Analytics'),
                              Gap(24.h),
                              Center(
                                child: Text(
                                  'Member analytics and statistics will be displayed here.',
                                  style: theme.textTheme.bodyLarge,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 2: Additional Info
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              additionalInfoSection,
                              Gap(24.h),
                              membershipInfoSection,
                              Gap(24.h),
                              systemInfoSection,
                            ],
                          ),
                        ),

                        // Tab 3: Groups
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberGroups(member: member.value!),
                        ),

                        // Tab 4: Transactions
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberTransactions(
                            memberId: widget.memberId,
                          ),
                        ),

                        // Tab 5: Relationships
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberRelationships(
                            memberId: widget.memberId,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(32.h),
                ],
              ),
            ),
          );
        }
        // Tablet layout
        else if (isTablet) {
          return DefaultTabController(
            length: 5,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(24.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile header
                  profileHeader,
                  Gap(32.h),

                  // Primary info in two columns
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: contactInfoSection),
                      Gap(24.w),
                      Expanded(child: personalInfoSection),
                    ],
                  ),
                  Gap(32.h),

                  // Tab bar
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        width: 1.r,
                      ),
                    ),
                    child: TabBar(
                      tabs: [
                        Tab(text: 'Analytics'),
                        Tab(text: 'Additional Info'),
                        Tab(text: 'Groups'),
                        Tab(text: 'Transactions'),
                        Tab(text: 'Relationships'),
                      ],
                      labelColor: theme.colorScheme.onPrimary,
                      unselectedLabelColor: theme.colorScheme.onSurface
                          .withOpacity(0.7),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicatorWeight: 3,
                      indicator: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.all(4.r),
                      labelPadding: EdgeInsets.symmetric(horizontal: 8.r),
                    ),
                  ),

                  // Tab content
                  SizedBox(
                    height: 650.h, // Fixed height for tab content
                    child: TabBarView(
                      children: [
                        // Tab 1: Analytics
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeader(title: 'Member Analytics'),
                              Gap(24.h),
                              Center(
                                child: Text(
                                  'Member analytics and statistics will be displayed here.',
                                  style: theme.textTheme.bodyLarge,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 2: Additional Info
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(child: additionalInfoSection),
                              Gap(24.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    membershipInfoSection,
                                    Gap(24.h),
                                    systemInfoSection,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 3: Groups
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberGroups(member: member.value!),
                        ),

                        // Tab 4: Transactions
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberTransactions(
                            memberId: widget.memberId,
                          ),
                        ),

                        // Tab 5: Relationships
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberRelationships(
                            memberId: widget.memberId,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(32.h),
                ],
              ),
            ),
          );
        }
        // Desktop Small layout
        else if (isDesktopSmall) {
          // Calculate responsive widths for desktop
          final profileWidth = screenWidth * 0.25; // 25% of screen width
          final maxProfileWidth = 350.0; // Maximum width for profile section
          final minProfileWidth = 280.0; // Minimum width for profile section

          // Constrain profile width between min and max values
          final actualProfileWidth = profileWidth.clamp(
            minProfileWidth,
            maxProfileWidth,
          );
          // Create the profile card
          final profileCard = Card(
            elevation: 3,
            shadowColor: theme.shadowColor.withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
              side: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.1),
                width: 1.r,
              ),
            ),
            child: Padding(padding: EdgeInsets.all(32.r), child: profileHeader),
          );

          // Desktop Small layout with tabs
          return DefaultTabController(
            length: 5,
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 32.r, vertical: 24.r),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left column with profile
                      SizedBox(width: actualProfileWidth, child: profileCard),
                      Gap(32.w),
                      // Right section with primary information
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Welcome message at the top
                            WelcomeWidget(
                              title: 'Member Profile',
                              subtitle:
                                  'View and manage all details for this member. Use the edit button to update information.',
                              icon: IconlyLight.infoSquare,
                            ),
                            Gap(24.h),

                            // Primary info in a single column for desktop small
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // First section
                                contactInfoSection,
                                Gap(24.h),
                                // Second section
                                personalInfoSection,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // Tab bar
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        width: 1.r,
                      ),
                    ),
                    child: TabBar(
                      tabs: [
                        Tab(text: 'Analytics'),
                        Tab(text: 'Additional Info'),
                        Tab(text: 'Groups'),
                        Tab(text: 'Transactions'),
                        Tab(text: 'Relationships'),
                      ],
                      labelColor: theme.colorScheme.onPrimary,
                      unselectedLabelColor: theme.colorScheme.onSurface
                          .withOpacity(0.7),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicatorWeight: 3,
                      indicator: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.all(4.r),
                      labelPadding: EdgeInsets.symmetric(horizontal: 8.r),
                    ),
                  ),

                  // Tab content
                  SizedBox(
                    height: 700.h, // Fixed height for tab content
                    child: TabBarView(
                      children: [
                        // Tab 1: Analytics
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeader(title: 'Member Analytics'),
                              Gap(24.h),
                              Center(
                                child: Text(
                                  'Member analytics and statistics will be displayed here.',
                                  style: theme.textTheme.bodyLarge,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 2: Additional Info
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              additionalInfoSection,
                              Gap(24.h),
                              membershipInfoSection,
                              Gap(24.h),
                              systemInfoSection,
                            ],
                          ),
                        ),

                        // Tab 3: Groups
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberGroups(member: m),
                        ),

                        // Tab 4: Transactions
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberTransactions(
                            memberId: widget.memberId,
                          ),
                        ),

                        // Tab 5: Relationships
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberRelationships(
                            memberId: widget.memberId,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          // Calculate responsive widths for desktop
          final profileWidth = screenWidth * 0.25; // 25% of screen width
          final maxProfileWidth = 350.0; // Maximum width for profile section
          final minProfileWidth = 280.0; // Minimum width for profile section

          // Constrain profile width between min and max values
          final actualProfileWidth = profileWidth.clamp(
            minProfileWidth,
            maxProfileWidth,
          );
          // Create the profile card
          final profileCard = Card(
            elevation: 3,
            shadowColor: theme.shadowColor.withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
              side: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.1),
                width: 1.r,
              ),
            ),
            child: Padding(padding: EdgeInsets.all(32.r), child: profileHeader),
          );

          // Desktop Large layout with tabs
          return DefaultTabController(
            length: 5,
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 40.r, vertical: 32.r),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left column with profile
                      SizedBox(width: actualProfileWidth, child: profileCard),
                      Gap(40.w),
                      // Right section with primary information
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Welcome message at the top
                            WelcomeWidget(
                              title: 'Member Profile',
                              subtitle:
                                  'View and manage all details for this member. Use the edit button to update information.',
                              icon: IconlyLight.infoSquare,
                            ),
                            Gap(24.h),

                            // Primary info in two columns for desktop large
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // First column
                                Expanded(child: contactInfoSection),
                                Gap(32.w),
                                // Second column
                                Expanded(child: personalInfoSection),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(40.h),

                  // Tab bar
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        width: 1.r,
                      ),
                    ),
                    child: TabBar(
                      tabs: [
                        Tab(text: 'Analytics'),
                        Tab(text: 'Additional Info'),
                        Tab(text: 'Groups'),
                        Tab(text: 'Transactions'),
                        Tab(text: 'Relationships'),
                      ],
                      labelColor: theme.colorScheme.onPrimary,
                      unselectedLabelColor: theme.colorScheme.onSurface
                          .withOpacity(0.7),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicatorWeight: 3,
                      indicator: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.all(4.r),
                      labelPadding: EdgeInsets.symmetric(horizontal: 8.r),
                    ),
                  ),

                  // Tab content
                  SizedBox(
                    height: 700.h, // Fixed height for tab content
                    child: TabBarView(
                      children: [
                        // Tab 1: Analytics
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeader(title: 'Member Analytics'),
                              Gap(24.h),
                              Center(
                                child: Text(
                                  'Member analytics and statistics will be displayed here.',
                                  style: theme.textTheme.bodyLarge,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 2: Additional Info
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(child: additionalInfoSection),
                              Gap(32.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    membershipInfoSection,
                                    Gap(32.h),
                                    systemInfoSection,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Tab 3: Groups
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberGroups(member: m),
                        ),

                        // Tab 4: Transactions
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberTransactions(
                            memberId: widget.memberId,
                          ),
                        ),

                        // Tab 5: Relationships
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 24.h),
                          child: ShowMemberRelationships(
                            memberId: widget.memberId,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      }),
      floatingActionButton:
          (isDesktopSmall || isDesktopLarge)
              ? null
              : FloatingActionButton.extended(
                onPressed: () {
                  context.go(Routes.MEMBERS);
                },
                icon: const Icon(IconlyLight.arrowLeft),
                label: const Text('Back to Members'),
                elevation: 6,
              ),
    );
  }
}
