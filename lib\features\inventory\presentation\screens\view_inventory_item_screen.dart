import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/inventory_item_controller.dart';
import '../../controllers/inventory_controller.dart';
import '../../models/inventory_item_model.dart';
import '../../models/inventory_record_model.dart';

class ViewInventoryItemScreen extends StatefulWidget {
  final String itemId;

  const ViewInventoryItemScreen({super.key, required this.itemId});

  @override
  State<ViewInventoryItemScreen> createState() =>
      _ViewInventoryItemScreenState();
}

class _ViewInventoryItemScreenState extends State<ViewInventoryItemScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final Rx<InventoryItemModel?> _item = Rx<InventoryItemModel?>(null);
  final RxBool _isLoading = true.obs;
  final RxBool _isLoadingRecords = true.obs;
  final RxList<InventoryRecordModel> _itemRecords =
      <InventoryRecordModel>[].obs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadItemData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadItemData() async {
    _isLoading.value = true;
    try {
      final itemController = Get.find<InventoryItemController>();
      final item = await itemController.getInventoryItemById(widget.itemId);
      _item.value = item;

      // Load records for this item
      await _loadItemRecords();
    } catch (e) {
      debugPrint('Error loading item: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _loadItemRecords() async {
    _isLoadingRecords.value = true;
    try {
      final inventoryController = Get.find<InventoryController>();
      // Set item filter and fetch records
      inventoryController.setItemFilter(widget.itemId);
      await inventoryController.fetchInventoryRecords();
      _itemRecords.value =
          inventoryController.inventoryRecords
              .where((record) => record.inventoryItemId == widget.itemId)
              .toList();
    } catch (e) {
      debugPrint('Error loading item records: $e');
    } finally {
      _isLoadingRecords.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: const Text('Item Details'),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.edit),
            onPressed: () {
              // Navigate to edit screen when implemented
              Get.snackbar(
                'Edit Item',
                'Edit functionality will be implemented',
                snackPosition: SnackPosition.bottom,
              );
            },
            tooltip: 'Edit Item',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(IconlyLight.document), text: 'Details'),
            Tab(icon: Icon(IconlyLight.activity), text: 'Logs'),
          ],
        ),
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_item.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  IconlyLight.dangerCircle,
                  size: 48,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text('Item not found'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go(Routes.INVENTORY_ITEMS),
                  child: const Text('Back to Items'),
                ),
              ],
            ),
          );
        }

        return TabBarView(
          controller: _tabController,
          children: [_buildDetailsTab(), _buildLogsTab()],
        );
      }),
    );
  }

  Widget _buildDetailsTab() {
    final item = _item.value!;

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildItemHeader(item),
          SizedBox(height: 24.h),
          _buildItemDetails(item),
          SizedBox(height: 24.h),
          if (item.hasMedia) _buildMediaSection(item),
        ],
      ),
    );
  }

  Widget _buildLogsTab() {
    return Obx(() {
      if (_isLoadingRecords.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_itemRecords.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(IconlyLight.document, size: 64, color: Colors.grey.shade300),
              const SizedBox(height: 16),
              Text(
                'No transaction logs found',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'Transaction history will appear here',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _itemRecords.length,
        itemBuilder: (context, index) {
          final record = _itemRecords[index];
          return _buildRecordCard(record);
        },
      );
    });
  }

  Widget _buildItemHeader(InventoryItemModel item) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: colorScheme.primary.withOpacity(0.1),
              radius: 30,
              backgroundImage:
                  item.hasMedia && item.firstMediaUrl != null
                      ? NetworkImage(item.firstMediaUrl!)
                      : null,
              child:
                  item.hasMedia && item.firstMediaUrl != null
                      ? null
                      : Icon(
                        IconlyLight.category,
                        size: 30,
                        color: colorScheme.primary,
                      ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title ?? 'Unknown Item',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (item.barcode != null && item.barcode!.isNotEmpty)
                    Text(
                      'Barcode: ${item.barcode}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  SizedBox(height: 8.h),
                  _buildStatusChip(item.status),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final isActive = status.toLowerCase() == 'active';
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      decoration: BoxDecoration(
        color:
            isActive
                ? Colors.green.withOpacity(0.1)
                : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isActive
                  ? Colors.green.withOpacity(0.3)
                  : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: isActive ? Colors.green : Colors.red,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildItemDetails(InventoryItemModel item) {
    final theme = Theme.of(context);
    final itemController = Get.find<InventoryItemController>();
    final unitOfMeasure = itemController.getUnitOfMeasureById(
      item.unitOfMeasureId ?? '',
    );

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Item Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildDetailRow(
              'Category',
              item.category ?? 'Unknown',
              IconlyLight.category,
            ),
            _buildDetailRow(
              'Unit of Measure',
              unitOfMeasure?.fullDisplay ?? 'Unknown',
              IconlyLight.chart,
            ),
            if (item.quantity != null)
              _buildDetailRow(
                'Quantity',
                item.quantity.toString(),
                IconlyLight.bag,
              ),
            if (item.description != null && item.description!.isNotEmpty)
              _buildDetailRow(
                'Description',
                item.description!,
                IconlyLight.document,
              ),
            _buildDetailRow(
              'Created',
              item.createdAt != null
                  ? DateFormat('MMM dd, yyyy HH:mm').format(item.createdAt!)
                  : 'Unknown',
              IconlyLight.calendar,
            ),
            if (item.createdByUser != null)
              _buildDetailRow(
                'Created By',
                '${item.createdByUser!.firstName ?? ''} ${item.createdByUser!.secondName ?? ''}'
                    .trim(),
                IconlyLight.profile,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade600),
          SizedBox(width: 8.w),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value, style: theme.textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaSection(InventoryItemModel item) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Media',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              height: 100.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: item.media?.length ?? 0,
                itemBuilder: (context, index) {
                  final media = item.media![index];
                  return Container(
                    margin: EdgeInsets.only(right: 8.w),
                    width: 100.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(media.mediaUrl ?? ''),
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordCard(InventoryRecordModel record) {
    final theme = Theme.of(context);

    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  record.inventoryType ?? 'Unknown Type',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  record.createdAt != null
                      ? DateFormat('MMM dd, yyyy').format(record.createdAt!)
                      : 'Unknown Date',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            if (record.quantity != null)
              Text(
                'Quantity: ${record.quantity}',
                style: theme.textTheme.bodyMedium,
              ),
            if (record.condition != null)
              Text(
                'Condition: ${record.condition}',
                style: theme.textTheme.bodyMedium,
              ),
            if (record.notes != null && record.notes!.isNotEmpty)
              Text('Notes: ${record.notes}', style: theme.textTheme.bodyMedium),
            if (record.displayName.isNotEmpty)
              Text(
                'By: ${record.displayName}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
