import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/media_upload/presentation/widgets/media_upload_widget.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:onechurch/features/members/presentation/screens/members_screen/members_screen.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/constants/enums.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../controllers/group_controller.dart';

class CreateGroupScreen extends StatefulWidget {
  const CreateGroupScreen({super.key});

  @override
  State<CreateGroupScreen> createState() => _CreateGroupScreenState();
}

class _CreateGroupScreenState extends State<CreateGroupScreen> {
  final GroupController _controller = Get.find<GroupController>();
  final MemberController _memberController = Get.find<MemberController>();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _controller.clearFormFields();
  }

  @override
  void dispose() {
    // Clear selected members when leaving the screen
    _memberController.selectedMembers.clear();
    super.dispose();
  }

  // Show role selection dialog
  Future<void> _showRoleSelectionDialog(String memberId, String currentRole) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return _RoleSelectionDialog(
          memberId: memberId,
          currentRole: currentRole,
          onRoleSelected: (String role) {
            _controller.addMember(memberId, role);
          },
        );
      },
    );
  }

  // Check if at least one admin exists
  bool _hasAtLeastOneAdmin() {
    return _controller.selectedMembers.any(
      (member) => member['role'] == GroupMemberRole.admin.displayName,
    );
  }

  // Show admin validation error
  void _showAdminValidationError() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('At least one member must be assigned as an admin'),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Sync members between controllers
  void _syncMembers() {
    // Remove group members that are no longer in selected members
    _controller.selectedMembers.removeWhere((groupMember) {
      final memberId = groupMember['member_id'];
      return !_memberController.selectedMembers.any((m) => m.id == memberId);
    });

    // Add new members with default role
    for (final member in _memberController.selectedMembers) {
      final existingMember = _controller.selectedMembers.firstWhereOrNull(
        (m) => m['member_id'] == member.id,
      );

      if (existingMember == null) {
        _controller.addMember(
          member.id ?? '',
          GroupMemberRole.member.displayName,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Sync members between controllers
    _syncMembers();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Group'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(Routes.GROUPS);
          },
        ),
      ),
      body: Obx(() {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Section
                const Text(
                  'Basic Information',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Gap(16),
                CustomTextField(
                  label: 'Group Title',
                  controller: _controller.titleController,
                  hintText: 'Enter group title',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a title';
                    }
                    return null;
                  },
                ),
                const Gap(16),
                CustomTextField(
                  label: 'Description',
                  controller: _controller.descriptionController,
                  hintText: 'Enter group description',
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),

                const Gap(24),

                // Media Section
                const Text(
                  'Media',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Gap(16),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(16),
                        MediaUploadWidget(
                          category: 'GROUPS',
                          multipleSelect: true,
                          onMediaSelected: (selectedMedia) {
                            _controller.mediaItems.value = selectedMedia;
                          },
                        ),

                        const Gap(16),
                        if (_controller.mediaItems.isNotEmpty) ...[
                          const Text(
                            'Added Media:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const Gap(8),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.mediaItems.length,
                            itemBuilder: (context, index) {
                              final media = _controller.mediaItems[index];
                              return ListTile(
                                title: Text(media.title ?? 'Media Item'),
                                subtitle: Text(media.mediaUrl ?? ''),
                                leading: Icon(
                                  media.type == 'IMAGE'
                                      ? Icons.image
                                      : media.type == 'VIDEO'
                                      ? Icons.video_library
                                      : Icons.insert_drive_file,
                                ),
                                trailing: IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed: () {
                                    _controller.removeMediaItem(index);
                                  },
                                ),
                              );
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const Gap(24),

                const Gap(16),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Members Section
                        Row(
                          children: [
                            const Text(
                              'Members',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Spacer(),
                            CircleAvatar(
                              child: IconButton(
                                onPressed: () async {
                                  // Navigate to member selection screen
                                  final result = await Navigator.push<
                                    List<Map<String, dynamic>>
                                  >(
                                    context,
                                    MaterialPageRoute<
                                      List<Map<String, dynamic>>
                                    >(
                                      builder:
                                          (BuildContext context) =>
                                              MemberViewScreen(isSelect: true),
                                    ),
                                  );

                                  // Sync selected members with group controller
                                  if (result != null) {
                                    for (final member
                                        in _memberController.selectedMembers) {
                                      // Add member with default role if not already added
                                      final existingMember = _controller
                                          .selectedMembers
                                          .firstWhereOrNull(
                                            (m) => m['member_id'] == member.id,
                                          );

                                      if (existingMember == null) {
                                        _controller.addMember(
                                          member.id ?? '',
                                          GroupMemberRole.member.displayName,
                                        );
                                      }
                                    }
                                  }
                                },
                                icon: const Icon(IconlyLight.addUser),
                              ),
                            ),
                          ],
                        ),
                        Gap(12),

                        const SizedBox(width: double.infinity),
                        if (_memberController.selectedMembers.isNotEmpty) ...[
                          const Text(
                            'Selected Members:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const Gap(8),

                          Obx(
                            () => ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount:
                                  _memberController.selectedMembers.length,
                              itemBuilder: (context, index) {
                                final member =
                                    _memberController.selectedMembers[index];

                                // Find the corresponding group member data
                                final groupMember = _controller.selectedMembers
                                    .firstWhereOrNull(
                                      (m) => m['member_id'] == member.id,
                                    );

                                final currentRole =
                                    groupMember?['role'] ??
                                    GroupMemberRole.member.displayName;

                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  child: ListTile(
                                    title: Text(
                                      "${member.firstName ?? ''} ${member.secondName ?? ''}",
                                    ),
                                    subtitle: Text('Role: $currentRole'),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: const Icon(
                                            Icons.edit,
                                            color: Colors.blue,
                                          ),
                                          onPressed: () async {
                                            await _showRoleSelectionDialog(
                                              member.id ?? '',
                                              currentRole,
                                            ).whenComplete(() {
                                              setState(() {});
                                            });
                                          },
                                        ),
                                        IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          onPressed: () {
                                            _controller.removeMember(
                                              member.id ?? '',
                                            );
                                            _memberController.selectedMembers
                                                .removeWhere(
                                                  (m) => m.id == member.id,
                                                );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const Gap(32),

                if (_controller.errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      _controller.errorMessage.value,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),

                Center(
                  child: SizedBox(
                    width: 200,
                    child: CustomButton(
                      onPressed:
                          _controller.isSubmitting.value
                              ? () {}
                              : () {
                                if (_formKey.currentState!.validate()) {
                                  // Check if at least one admin exists
                                  if (!_hasAtLeastOneAdmin()) {
                                    _showAdminValidationError();
                                    return;
                                  }

                                  _controller.createGroup().then((
                                    success,
                                  ) async {
                                    if (success && context.mounted) {
                                      // Refresh the groups list to reflect the new group
                                      await _controller.refreshGroups();

                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Group created successfully',
                                          ),
                                          backgroundColor: Colors.green,
                                        ),
                                      );
                                      context.go(Routes.GROUPS);
                                    }
                                  });
                                }
                              },
                      text: 'Create Group',
                      isLoading: _controller.isSubmitting.value,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}

// Role Selection Dialog Widget
class _RoleSelectionDialog extends StatefulWidget {
  final String memberId;
  final String currentRole;
  final Function(String) onRoleSelected;

  const _RoleSelectionDialog({
    required this.memberId,
    required this.currentRole,
    required this.onRoleSelected,
  });

  @override
  State<_RoleSelectionDialog> createState() => _RoleSelectionDialogState();
}

class _RoleSelectionDialogState extends State<_RoleSelectionDialog> {
  late String selectedRole;

  @override
  void initState() {
    super.initState();
    selectedRole = widget.currentRole;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Role'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            GroupMemberRole.values.map((role) {
              return RadioListTile<String>(
                title: Text(role.displayName),
                value: role.displayName,
                groupValue: selectedRole,
                onChanged: (String? value) {
                  if (value != null) {
                    setState(() {
                      selectedRole = value;
                    });
                  }
                },
              );
            }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            widget.onRoleSelected(selectedRole);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
