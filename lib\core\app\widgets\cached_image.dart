import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;

/// A widget that displays a network image with caching capabilities.
/// 
/// This widget handles loading states, error states, and caching of network images
/// to improve performance and user experience.
class CachedImage extends StatefulWidget {
  /// The URL of the image to display
  final String imageUrl;
  
  /// Optional width of the image
  final double? width;
  
  /// Optional height of the image
  final double? height;
  
  /// Optional size that overrides both width and height when provided
  final double? size;
  
  /// How to inscribe the image into the space allocated
  final BoxFit? fit;
  
  /// Optional widget to display while the image is loading
  final Widget? loadingWidget;
  
  /// Optional widget to display when an error occurs
  final Widget? errorWidget;
  
  /// Optional border radius for the image
  final BorderRadius? borderRadius;
  
  /// Optional color to blend with the image
  final Color? color;
  
  /// Optional blend mode for the color
  final BlendMode? colorBlendMode;
  
  /// Optional alignment of the image within its bounds
  final Alignment alignment;
  
  /// Optional filter quality for the image
  final FilterQuality filterQuality;
  
  /// Optional duration for the cache
  final Duration cacheDuration;

  const CachedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.size,
    this.fit = BoxFit.cover,
    this.loadingWidget,
    this.errorWidget,
    this.borderRadius,
    this.color,
    this.colorBlendMode,
    this.alignment = Alignment.center,
    this.filterQuality = FilterQuality.low,
    this.cacheDuration = const Duration(days: 7),
  });

  @override
  State<CachedImage> createState() => _CachedImageState();
}

class _CachedImageState extends State<CachedImage> {
  File? _cachedFile;
  bool _isLoading = true;
  bool _hasError = false;
  static final Map<String, File> _memoryCache = {};

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _isLoading = true;
      _hasError = false;
      _cachedFile = null;
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (widget.imageUrl.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    try {
      // Generate a unique key for the image URL
      final String cacheKey = _generateCacheKey(widget.imageUrl);

      // Check memory cache first
      if (_memoryCache.containsKey(cacheKey)) {
        final File cachedFile = _memoryCache[cacheKey]!;
        if (await cachedFile.exists()) {
          setState(() {
            _cachedFile = cachedFile;
            _isLoading = false;
            _hasError = false;
          });
          return;
        }
      }

      // Check disk cache
      final File file = await _getCachedFile(cacheKey);
      if (await file.exists()) {
        final DateTime fileInfo = await file.lastModified();
        final bool isExpired = DateTime.now().difference(fileInfo) > widget.cacheDuration;

        if (!isExpired) {
          // Cache is valid, use it
          setState(() {
            _cachedFile = file;
            _isLoading = false;
            _hasError = false;
          });
          _memoryCache[cacheKey] = file;
          return;
        }
      }

      // Download the image
      final http.Response response = await http.get(Uri.parse(widget.imageUrl));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        setState(() {
          _cachedFile = file;
          _isLoading = false;
          _hasError = false;
        });
        _memoryCache[cacheKey] = file;
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  String _generateCacheKey(String url) {
    return md5.convert(utf8.encode(url)).toString();
  }

  Future<File> _getCachedFile(String cacheKey) async {
    final Directory cacheDir = await getTemporaryDirectory();
    final String cachePath = '${cacheDir.path}/cached_images';
    await Directory(cachePath).create(recursive: true);
    return File('$cachePath/$cacheKey');
  }

  @override
  Widget build(BuildContext context) {
    final double effectiveWidth = widget.size ?? widget.width ?? double.infinity;
    final double effectiveHeight = widget.size ?? widget.height ?? double.infinity;

    if (_isLoading) {
      return SizedBox(
        width: effectiveWidth,
        height: effectiveHeight,
        child: widget.loadingWidget ?? const Center(child: CircularProgressIndicator()),
      );
    }

    if (_hasError || _cachedFile == null) {
      return SizedBox(
        width: effectiveWidth,
        height: effectiveHeight,
        child: widget.errorWidget ?? 
          Container(
            color: Colors.grey[200],
            child: const Center(child: Icon(Icons.image_not_supported_outlined, color: Colors.grey)),
          ),
      );
    }

    final Widget imageWidget = Image.file(
      _cachedFile!,
      width: effectiveWidth,
      height: effectiveHeight,
      fit: widget.fit,
      color: widget.color,
      colorBlendMode: widget.colorBlendMode,
      alignment: widget.alignment,
      filterQuality: widget.filterQuality,
      errorBuilder: (context, error, stackTrace) {
        return SizedBox(
          width: effectiveWidth,
          height: effectiveHeight,
          child: widget.errorWidget ?? 
            Container(
              color: Colors.grey[200],
              child: const Center(child: Icon(Icons.image_not_supported_outlined, color: Colors.grey)),
            ),
        );
      },
    );

    if (widget.borderRadius != null) {
      return ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}