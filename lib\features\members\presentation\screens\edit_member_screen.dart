import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../data/models/member_model.dart';
import '../../controllers/member_controller.dart';
import '../dialogs/member_category_selection_dialog.dart';

class EditMemberScreen extends StatefulWidget {
  final String memberId;

  const EditMemberScreen({super.key, required this.memberId});

  @override
  State<EditMemberScreen> createState() => _EditMemberScreenState();
}

class _EditMemberScreenState extends State<EditMemberScreen> {
  final memberController = Get.find<MemberController>();
  final logger = Get.find<Logger>();
  final isLoading = true.obs;
  final errorMessage = ''.obs;
  final member = Rx<MemberModel?>(null);

  // Form key and controllers
  final _formKey = GlobalKey<FormState>();
  late TextEditingController firstNameController;
  late TextEditingController secondNameController;
  late TextEditingController phoneController;
  late TextEditingController secondaryPhoneController;
  late TextEditingController emailController;
  late TextEditingController idNumberController;
  late TextEditingController addressController;
  late TextEditingController nationalityController;
  late TextEditingController occupationController;
  late TextEditingController tribeController;
  late TextEditingController incomeBracketController;

  // Form values
  Rx<DateTime?> joinDate = Rx<DateTime?>(null);
  Rx<DateTime?> dob = Rx<DateTime?>(null);
  Rx<DateTime?> baptismDate = Rx<DateTime?>(null);
  RxString gender = 'MALE'.obs;
  RxString maritalStatus = 'SINGLE'.obs;
  RxString educationLevel = 'PRIMARY'.obs;
  RxString disability = 'NONE'.obs;
  RxString employmentStatus = 'EMPLOYED'.obs;
  RxInt householdSize = 2.obs;
  RxString housingType = 'PERMANENT'.obs;
  RxList<String> assetOwnership = <String>['LAND', 'LIVESTOCK', 'BUSINESS'].obs;
  RxString locationId = ''.obs;
  Rx<dynamic> selectedCategory = Rx<dynamic>(null);

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    firstNameController = TextEditingController();
    secondNameController = TextEditingController();
    phoneController = TextEditingController();
    secondaryPhoneController = TextEditingController();
    emailController = TextEditingController();
    idNumberController = TextEditingController();
    addressController = TextEditingController();
    nationalityController = TextEditingController();
    occupationController = TextEditingController();
    tribeController = TextEditingController();
    incomeBracketController = TextEditingController();

    _loadMember();
  }

  @override
  void dispose() {
    // Dispose controllers
    firstNameController.dispose();
    secondNameController.dispose();
    phoneController.dispose();
    secondaryPhoneController.dispose();
    emailController.dispose();
    idNumberController.dispose();
    addressController.dispose();
    nationalityController.dispose();
    occupationController.dispose();
    tribeController.dispose();
    incomeBracketController.dispose();
    super.dispose();
  }

  Future<void> _loadMember() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // Fetch the member data
      final result = await memberController.getMemberById(widget.memberId);

      if (result != null) {
        member.value = result;
        _populateFormFields(result);
      } else {
        errorMessage.value = 'Member not found';
        ToastUtils.showErrorToast(errorMessage.value, null);
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
    } finally {
      isLoading.value = false;
    }
  }

  void _populateFormFields(MemberModel member) {
    // Populate text controllers
    firstNameController.text = member.firstName ?? '';
    secondNameController.text = member.secondName ?? '';
    phoneController.text = member.phoneNumber ?? '';
    secondaryPhoneController.text = member.secondaryNumber ?? '';
    emailController.text = member.email ?? '';
    idNumberController.text = member.idNumber ?? '';
    addressController.text = member.address ?? '';
    nationalityController.text = member.nationality ?? 'Kenyan';
    occupationController.text = member.occupation ?? 'FARMER';
    tribeController.text = member.tribe ?? '';
    incomeBracketController.text = member.incomeBracket ?? '<10k';

    // Populate date values
    joinDate.value = member.joinDate;
    dob.value = member.dob;
    baptismDate.value =
        member.baptismDate != null
            ? (member.baptismDate is DateTime ? member.baptismDate : null)
            : null;

    // Populate dropdown values
    gender.value = member.gender ?? 'MALE';
    maritalStatus.value = member.maritalStatus ?? 'SINGLE';
    educationLevel.value = member.educationLevel ?? 'PRIMARY';
    disability.value = member.disability ?? 'NONE';
    employmentStatus.value = member.employmentStatus ?? 'EMPLOYED';
    householdSize.value = member.householdSize ?? 2;
    housingType.value = member.housingType ?? 'PERMANENT';

    // Populate asset ownership
    if (member.assetOwnership != null) {
      if (member.assetOwnership is List) {
        assetOwnership.value = member.assetOwnership ?? [];
      }
    }

    // Populate location and category
    locationId.value = member.locationId?.toString() ?? '';
    selectedCategory.value = member.memberCategory;
  }

  Future<void> _selectDate(
    BuildContext context,
    Rx<DateTime?> dateValue,
    String title,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: dateValue.value ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      helpText: title,
    );

    if (picked != null && picked != dateValue.value) {
      dateValue.value = picked;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Select Date';
    return DateFormat('dd MMM yyyy').format(date);
  }

  void _showCategorySelectionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => MemberCategorySelectionDialog(
            onCategorySelected: (category) {
              selectedCategory.value = category;
            },
          ),
    );
  }

  Future<void> _updateMember() async {
    if (!_formKey.currentState!.validate()) {
      ToastUtils.showErrorToast('Please fill all required fields', null);
      return;
    }

    try {
      isLoading.value = true;

      // Create member data map
      final Map<String, dynamic> memberData = {
        // Personal information
        'first_name': firstNameController.text,
        'second_name': secondNameController.text,
        'phone_number': phoneController.text,
        'secondary_number': secondaryPhoneController.text,
        'email': emailController.text,
        'id_number': idNumberController.text,

        // IDs and references
        'organisation_id': member.value?.organisationId ?? '',
        'member_category_id': selectedCategory.value?.id,
        'location_id': locationId.value,
        'user_id': member.value?.userId ?? 1,

        // Account information
        'account_number': phoneController.text,
        'balance': member.value?.balance ?? 0,

        // Address and location
        'address': addressController.text,

        // Personal attributes
        'gender': gender.value,
        'marital_status': maritalStatus.value,
        'occupation': occupationController.text,
        'education_level': educationLevel.value,
        'tribe': tribeController.text,
        'disability': disability.value,
        'nationality': nationalityController.text,

        // Socioeconomic information
        'income_bracket': incomeBracketController.text,
        'employment_status': employmentStatus.value,
        'household_size': householdSize.value,
        'housing_type': housingType.value,
        'asset_ownership': assetOwnership,

        // Profile information
        'profile_url': member.value?.profileUrl ?? '',
      };

      // Handle dates properly - convert DateTime to string with timezone
      // Join date
      if (joinDate.value != null) {
        memberData['join_date'] = joinDate.value!.toUtc().toIso8601String();
      } else {
        memberData['join_date'] = DateTime.now().toUtc().toIso8601String();
      }

      // Date of birth
      if (dob.value != null) {
        memberData['dob'] = dob.value!.toUtc().toIso8601String();
      } else {
        memberData['dob'] = DateTime.now().toUtc().toIso8601String();
      }

      // Baptism date
      if (baptismDate.value != null) {
        memberData['baptism_date'] =
            baptismDate.value!.toUtc().toIso8601String();
      } else {
        memberData['baptism_date'] = DateTime.now().toUtc().toIso8601String();
      }

      // Call the update service method
      final result = await memberController.updateMember(
        widget.memberId,
        memberData,
      );

      if (result) {
        ToastUtils.showSuccessToast('Member updated successfully', null);
        // Navigate back to member details
        if (context.mounted) {
          context.go('${Routes.MEMBERS}/${widget.memberId}');
        }
      }
    } catch (e) {
      errorMessage.value = 'Error updating member: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error updating member: $e');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Member'),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.delete),
            onPressed: () {
              // Show delete confirmation
              showDialog(
                context: context,
                builder:
                    (context) => AlertDialog(
                      title: const Text('Delete Member'),
                      content: const Text(
                        'Are you sure you want to delete this member? This action cannot be undone.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                        FilledButton(
                          onPressed: () {
                            // TODO: Implement delete functionality
                            Navigator.pop(context);
                            ToastUtils.showInfoToast(
                              'Delete functionality coming soon',
                              null,
                            );
                          },
                          style: FilledButton.styleFrom(
                            backgroundColor: Colors.red,
                          ),
                          child: const Text('Delete'),
                        ),
                      ],
                    ),
              );
            },
            tooltip: 'Delete Member',
          ),
        ],
      ),
      body: Obx(() {
        if (isLoading.value && member.value == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (errorMessage.isNotEmpty && member.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  IconlyLight.dangerCircle,
                  size: 48,
                  color: Colors.red,
                ),
                Gap(16.h),
                Text(
                  'Error Loading Member',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Gap(8.h),
                Text(errorMessage.value),
                Gap(24.h),
                FilledButton.icon(
                  onPressed: _loadMember,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                ),
              ],
            ),
          );
        }

        return Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Personal Information Section
                _buildSectionHeader(theme, 'Personal Information'),
                Gap(16.h),

                // First Name
                CustomTextFormField(
                  controller: firstNameController,

                  labelText: 'First Name *',
                  hintText: 'Enter first name',
                  prefixIcon: Icon(IconlyLight.profile),

                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'First name is required';
                    }
                    return null;
                  },
                ),
                Gap(16.h),

                // Second Name
                CustomTextFormField(
                  controller: secondNameController,

                  labelText: 'Second Name *',
                  hintText: 'Enter second name',
                  prefixIcon: Icon(IconlyLight.profile),

                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Second name is required';
                    }
                    return null;
                  },
                ),
                Gap(16.h),

                // Phone Number
                CustomTextFormField(
                  controller: phoneController,

                  labelText: 'Phone Number *',
                  hintText: 'Enter phone number',
                  prefixIcon: Icon(IconlyLight.call),

                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Phone number is required';
                    }
                    return null;
                  },
                ),
                Gap(16.h),

                // Secondary Phone
                CustomTextFormField(
                  controller: secondaryPhoneController,

                  labelText: 'Secondary Phone',
                  hintText: 'Enter secondary phone number',
                  prefixIcon: Icon(IconlyLight.call),

                  keyboardType: TextInputType.phone,
                ),
                Gap(16.h),

                // Email
                CustomTextFormField(
                  controller: emailController,

                  labelText: 'Email',
                  hintText: 'Enter email address',
                  prefixIcon: Icon(IconlyLight.message),

                  keyboardType: TextInputType.emailAddress,
                ),
                Gap(16.h),

                // ID Number
                CustomTextFormField(
                  controller: idNumberController,

                  labelText: 'ID Number',
                  hintText: 'Enter ID number',
                  prefixIcon: Icon(IconlyLight.document),
                ),
                Gap(16.h),

                // Address
                CustomTextFormField(
                  controller: addressController,

                  labelText: 'Address',
                  hintText: 'Enter address',
                  prefixIcon: Icon(IconlyLight.location),

                  maxLines: 2,
                ),
                Gap(24.h),

                // Dates Section
                _buildSectionHeader(theme, 'Important Dates'),
                Gap(16.h),

                // Join Date
                Obx(
                  () => ListTile(
                    title: const Text('Join Date'),
                    subtitle: Text(_formatDate(joinDate.value)),
                    leading: const Icon(IconlyLight.calendar),
                    trailing: const Icon(IconlyLight.arrowRight),
                    onTap:
                        () =>
                            _selectDate(context, joinDate, 'Select Join Date'),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                ),
                Gap(16.h),

                // Date of Birth
                Obx(
                  () => ListTile(
                    title: const Text('Date of Birth'),
                    subtitle: Text(_formatDate(dob.value)),
                    leading: const Icon(IconlyLight.calendar),
                    trailing: const Icon(IconlyLight.arrowRight),
                    onTap:
                        () => _selectDate(context, dob, 'Select Date of Birth'),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                ),
                Gap(16.h),

                // Baptism Date
                Obx(
                  () => ListTile(
                    title: const Text('Baptism Date'),
                    subtitle: Text(_formatDate(baptismDate.value)),
                    leading: const Icon(IconlyLight.calendar),
                    trailing: const Icon(IconlyLight.arrowRight),
                    onTap:
                        () => _selectDate(
                          context,
                          baptismDate,
                          'Select Baptism Date',
                        ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                ),
                Gap(24.h),

                // Personal Attributes Section
                _buildSectionHeader(theme, 'Personal Attributes'),
                Gap(16.h),

                // Gender
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: gender.value,
                    decoration: InputDecoration(
                      labelText: 'Gender',
                      prefixIcon: Icon(IconlyLight.profile),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'MALE', child: Text('Male')),
                      DropdownMenuItem(value: 'FEMALE', child: Text('Female')),
                      DropdownMenuItem(value: 'OTHER', child: Text('Other')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        gender.value = value;
                      }
                    },
                  ),
                ),
                Gap(16.h),

                // Marital Status
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: maritalStatus.value,
                    decoration: InputDecoration(
                      labelText: 'Marital Status',
                      prefixIcon: Icon(IconlyLight.heart),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'SINGLE', child: Text('Single')),
                      DropdownMenuItem(
                        value: 'MARRIED',
                        child: Text('Married'),
                      ),
                      DropdownMenuItem(
                        value: 'DIVORCED',
                        child: Text('Divorced'),
                      ),
                      DropdownMenuItem(
                        value: 'WIDOWED',
                        child: Text('Widowed'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        maritalStatus.value = value;
                      }
                    },
                  ),
                ),
                Gap(16.h),

                // Nationality
                CustomTextFormField(
                  controller: nationalityController,

                  labelText: 'Nationality',
                  hintText: 'Enter nationality',
                  prefixIcon: Icon(IconlyLight.document),
                ),
                Gap(16.h),

                // Tribe
                CustomTextFormField(
                  controller: tribeController,

                  labelText: 'Tribe',
                  hintText: 'Enter tribe',
                  prefixIcon: Icon(IconlyLight.profile),
                ),
                Gap(16.h),

                // Disability
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: disability.value,
                    decoration: InputDecoration(
                      labelText: 'Disability',
                      prefixIcon: Icon(IconlyLight.profile),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'NONE', child: Text('None')),
                      DropdownMenuItem(
                        value: 'PHYSICAL',
                        child: Text('Physical'),
                      ),
                      DropdownMenuItem(value: 'VISUAL', child: Text('Visual')),
                      DropdownMenuItem(
                        value: 'HEARING',
                        child: Text('Hearing'),
                      ),
                      DropdownMenuItem(
                        value: 'COGNITIVE',
                        child: Text('Cognitive'),
                      ),
                      DropdownMenuItem(value: 'OTHER', child: Text('Other')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        disability.value = value;
                      }
                    },
                  ),
                ),
                Gap(24.h),

                // Socioeconomic Information Section
                _buildSectionHeader(theme, 'Socioeconomic Information'),
                Gap(16.h),

                // Occupation
                CustomTextFormField(
                  controller: occupationController,

                  labelText: 'Occupation',
                  hintText: 'Enter occupation',
                  prefixIcon: Icon(IconlyLight.work),
                ),
                Gap(16.h),

                // Education Level
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: educationLevel.value,
                    decoration: InputDecoration(
                      labelText: 'Education Level',
                      prefixIcon: Icon(IconlyLight.document),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'NONE', child: Text('None')),
                      DropdownMenuItem(
                        value: 'PRIMARY',
                        child: Text('Primary'),
                      ),
                      DropdownMenuItem(
                        value: 'SECONDARY',
                        child: Text('Secondary'),
                      ),
                      DropdownMenuItem(
                        value: 'DIPLOMA',
                        child: Text('Diploma'),
                      ),
                      DropdownMenuItem(value: 'DEGREE', child: Text('Degree')),
                      DropdownMenuItem(
                        value: 'MASTERS',
                        child: Text('Masters'),
                      ),
                      DropdownMenuItem(value: 'PHD', child: Text('PhD')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        educationLevel.value = value;
                      }
                    },
                  ),
                ),
                Gap(16.h),

                // Employment Status
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: employmentStatus.value,
                    decoration: InputDecoration(
                      labelText: 'Employment Status',
                      prefixIcon: Icon(IconlyLight.work),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'EMPLOYED',
                        child: Text('Employed'),
                      ),
                      DropdownMenuItem(
                        value: 'SELF_EMPLOYED',
                        child: Text('Self-Employed'),
                      ),
                      DropdownMenuItem(
                        value: 'UNEMPLOYED',
                        child: Text('Unemployed'),
                      ),
                      DropdownMenuItem(
                        value: 'STUDENT',
                        child: Text('Student'),
                      ),
                      DropdownMenuItem(
                        value: 'RETIRED',
                        child: Text('Retired'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        employmentStatus.value = value;
                      }
                    },
                  ),
                ),
                Gap(16.h),

                // Income Bracket
                CustomTextFormField(
                  controller: incomeBracketController,

                  labelText: 'Income Bracket',
                  hintText: 'E.g. <10k, 10k-50k, 50k-100k, >100k',
                  prefixIcon: Icon(IconlyLight.wallet),
                ),
                Gap(16.h),

                // Household Size
                Obx(
                  () => DropdownButtonFormField<int>(
                    value: householdSize.value,
                    decoration: InputDecoration(
                      labelText: 'Household Size',
                      prefixIcon: Icon(IconlyLight.home),
                    ),
                    items: List.generate(
                      10,
                      (index) => DropdownMenuItem(
                        value: index + 1,
                        child: Text('${index + 1}'),
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        householdSize.value = value;
                      }
                    },
                  ),
                ),
                Gap(16.h),

                // Housing Type
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: housingType.value,
                    decoration: InputDecoration(
                      labelText: 'Housing Type',
                      prefixIcon: Icon(IconlyLight.home),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'PERMANENT',
                        child: Text('Permanent'),
                      ),
                      DropdownMenuItem(
                        value: 'SEMI_PERMANENT',
                        child: Text('Semi-Permanent'),
                      ),
                      DropdownMenuItem(
                        value: 'TEMPORARY',
                        child: Text('Temporary'),
                      ),
                      DropdownMenuItem(value: 'RENTAL', child: Text('Rental')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        housingType.value = value;
                      }
                    },
                  ),
                ),
                Gap(24.h),

                // Member Category Section
                _buildSectionHeader(theme, 'Member Category'),
                Gap(16.h),

                // Member Category
                Obx(
                  () => ListTile(
                    title: const Text('Member Category'),
                    subtitle: Text(
                      selectedCategory.value?.title ?? 'Select Category',
                    ),
                    leading: const Icon(IconlyLight.category),
                    trailing: const Icon(IconlyLight.arrowRight),
                    onTap: _showCategorySelectionDialog,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                ),
                Gap(32.h),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: Obx(
                    () => FilledButton.icon(
                      onPressed: isLoading.value ? null : _updateMember,
                      icon:
                          isLoading.value
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                              : const Icon(IconlyLight.edit),
                      label: Text(
                        isLoading.value ? 'Updating...' : 'Update Member',
                      ),
                    ),
                  ),
                ),
                Gap(16.h),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildSectionHeader(ThemeData theme, String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Divider(
          color: theme.colorScheme.primary.withOpacity(0.5),
          thickness: 2,
        ),
      ],
    );
  }
}
